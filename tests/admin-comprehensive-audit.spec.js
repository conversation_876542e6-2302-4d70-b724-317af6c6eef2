/**
 * 智慧养鹅后台管理中心 - 100%功能审查测试
 * 基于Context7最佳实践和Playwright测试框架
 * 
 * 测试目标：确保每一个按钮、链接、表单都能正常工作
 * 测试覆盖：所有页面、所有交互元素、所有业务流程
 */

const { test, expect } = require('@playwright/test');

// 测试数据和配置
const TEST_CONFIG = {
  adminCredentials: {
    username: 'admin',
    password: 'admin123'
  },
  baseUrl: 'http://localhost:4000',
  timeout: 10000,
  screenshotPath: 'screenshots/'
};

// 测试工具函数
class AdminTestHelper {
  constructor(page) {
    this.page = page;
  }

  // 等待并点击元素（带重试机制）
  async clickElement(selector, description = '') {
    try {
      await this.page.waitForSelector(selector, { timeout: 5000 });
      await this.page.click(selector);
      await this.page.waitForTimeout(500); // 等待UI响应
      console.log(`✅ 成功点击: ${description || selector}`);
    } catch (error) {
      console.log(`❌ 点击失败: ${description || selector} - ${error.message}`);
      throw error;
    }
  }

  // 截图保存
  async takeScreenshot(name, suffix = '') {
    const fullName = suffix ? `${name}-${suffix}` : name;
    await this.page.screenshot({ 
      path: `${TEST_CONFIG.screenshotPath}${fullName}.png`,
      fullPage: true 
    });
  }

  // 检查页面是否正常加载
  async checkPageLoaded(title, expectedElements = []) {
    await this.page.waitForLoadState('networkidle');
    
    // 检查页面标题或关键元素
    if (title) {
      const hasTitle = await this.page.locator(`text=${title}`).count() > 0;
      if (!hasTitle) {
        throw new Error(`页面标题 "${title}" 未找到`);
      }
    }

    // 检查预期元素
    for (const element of expectedElements) {
      await this.page.waitForSelector(element, { timeout: 3000 });
    }
  }

  // 填写表单
  async fillForm(formData) {
    for (const [selector, value] of Object.entries(formData)) {
      await this.page.fill(selector, value);
      await this.page.waitForTimeout(200);
    }
  }
}

test.describe('智慧养鹅后台管理中心 - 全面功能审查', () => {
  let helper;

  test.beforeEach(async ({ page }) => {
    helper = new AdminTestHelper(page);
    
    // 访问登录页面
    await page.goto(TEST_CONFIG.baseUrl);
    await helper.takeScreenshot('访问登录页面', 'before');
    
    // 执行登录
    await helper.fillForm({
      'input[name="username"]': TEST_CONFIG.adminCredentials.username,
      'input[name="password"]': TEST_CONFIG.adminCredentials.password
    });
    
    await helper.clickElement('button[type="submit"]', '登录按钮');
    await helper.checkPageLoaded('仪表盘', ['.sidebar', '.main-content']);
    await helper.takeScreenshot('访问登录页面', 'after');
  });

  test('01. 仪表盘导航测试', async ({ page }) => {
    await helper.takeScreenshot('仪表盘导航', 'before');
    
    // 测试仪表盘链接
    await helper.clickElement('a[href="/dashboard"]', '仪表盘导航');
    await helper.checkPageLoaded('仪表盘');
    
    await helper.takeScreenshot('仪表盘导航', 'after');
  });

  test('02. 租户管理模块测试', async ({ page }) => {
    await helper.takeScreenshot('租户管理导航', 'before');
    
    try {
      // 导航到租户管理
      await helper.clickElement('a[href="/tenants"]', '租户管理导航');
      await helper.checkPageLoaded('租户管理', ['.tenant-list', '.btn-primary']);
      
      await helper.takeScreenshot('租户管理导航', 'after');

      // 测试创建租户按钮
      await helper.takeScreenshot('创建租户按钮', 'before');
      const createBtn = await page.locator('.btn-primary').first();
      if (await createBtn.count() > 0) {
        await createBtn.click();
        await helper.takeScreenshot('创建租户按钮', 'after');
      }

      // 测试筛选功能
      const filterSelects = await page.locator('select').all();
      for (let i = 0; i < filterSelects.length; i++) {
        const select = filterSelects[i];
        const options = await select.locator('option').all();
        if (options.length > 1) {
          await select.selectOption({ index: 1 });
          await helper.takeScreenshot(`筛选选项-${i}`, 'after');
          await page.waitForTimeout(500);
        }
      }

      // 测试表格操作按钮
      const actionButtons = await page.locator('.btn').all();
      for (let i = 0; i < Math.min(actionButtons.length, 10); i++) {
        const btn = actionButtons[i];
        const text = await btn.textContent();
        
        if (text && text.includes('查看')) {
          await helper.takeScreenshot(`查看详情按钮(第${i+1}个)`, 'before');
          try {
            await btn.click();
            await helper.takeScreenshot(`查看详情按钮(第${i+1}个)`, 'after');
            await page.goBack();
          } catch (error) {
            await helper.takeScreenshot(`查看详情按钮(第${i+1}个)`, 'error');
          }
        }
      }
      
    } catch (error) {
      await helper.takeScreenshot('租户管理导航', 'error');
      throw error;
    }
  });

  test('03. 用户管理模块测试', async ({ page }) => {
    // 导航到用户管理
    await helper.clickElement('a[href="/users"]', '用户管理导航');
    await helper.checkPageLoaded('用户管理');

    // 测试所有按钮
    const buttons = await page.locator('button, .btn').all();
    for (let i = 0; i < buttons.length; i++) {
      const btn = buttons[i];
      const text = await btn.textContent();
      const isVisible = await btn.isVisible();
      
      if (isVisible && text) {
        await helper.takeScreenshot(`${text}按钮`, 'before');
        try {
          await btn.click();
          await helper.takeScreenshot(`${text}按钮`, 'after');
          await page.waitForTimeout(500);
          
          // 如果是模态框，尝试关闭
          const modal = page.locator('.modal, .popup');
          if (await modal.count() > 0) {
            await helper.clickElement('.modal .close, .popup .close', '关闭模态框');
          }
        } catch (error) {
          await helper.takeScreenshot(`${text}按钮`, 'error');
        }
      }
    }
  });

  test('04. 系统设置模块测试', async ({ page }) => {
    await helper.takeScreenshot('设置导航', 'before');
    
    // 导航到系统设置
    await helper.clickElement('a[href="/system"], a[href="/settings"]', '设置导航');
    await helper.checkPageLoaded('系统设置');
    
    await helper.takeScreenshot('设置导航', 'after');

    // 测试各个设置选项卡
    const tabs = await page.locator('.nav-tabs a, .tab-nav a').all();
    for (let i = 0; i < tabs.length; i++) {
      const tab = tabs[i];
      const tabText = await tab.textContent();
      
      if (tabText) {
        await helper.takeScreenshot(`设置选项卡-${tabText}`, 'before');
        try {
          await tab.click();
          await helper.takeScreenshot(`设置选项卡-${tabText}`, 'after');
          await page.waitForTimeout(1000);
        } catch (error) {
          await helper.takeScreenshot(`设置选项卡-${tabText}`, 'error');
        }
      }
    }
  });

  test('05. 监控和日志模块测试', async ({ page }) => {
    await helper.takeScreenshot('监控导航', 'before');
    
    // 测试监控页面
    try {
      await helper.clickElement('a[href="/monitoring"]', '监控导航');
      await helper.checkPageLoaded('性能监控');
      await helper.takeScreenshot('监控导航', 'after');
    } catch (error) {
      await helper.takeScreenshot('监控导航', 'error');
    }

    // 测试日志页面  
    try {
      await helper.clickElement('a[href="/logs"]', '日志导航');
      await helper.checkPageLoaded('系统日志');
      await helper.takeScreenshot('日志导航', 'after');
    } catch (error) {
      await helper.takeScreenshot('日志导航', 'error');
    }
  });

  test('06. 侧边栏功能测试', async ({ page }) => {
    await helper.takeScreenshot('侧边栏切换按钮', 'before');
    
    // 测试侧边栏收缩/展开
    const sidebarToggle = page.locator('.sidebar-toggle, .nav-toggle, [data-toggle="sidebar"]');
    if (await sidebarToggle.count() > 0) {
      await sidebarToggle.click();
      await helper.takeScreenshot('侧边栏切换按钮', 'after');
    }

    // 测试所有侧边栏菜单项
    const menuItems = await page.locator('.sidebar nav a, .menu-item').all();
    for (let i = 0; i < Math.min(menuItems.length, 15); i++) {
      const item = menuItems[i];
      const text = await item.textContent();
      
      if (text && text.trim()) {
        await helper.takeScreenshot(`菜单项-${text.trim()}`, 'before');
        try {
          await item.click();
          await page.waitForTimeout(1000);
          await helper.takeScreenshot(`菜单项-${text.trim()}`, 'after');
        } catch (error) {
          await helper.takeScreenshot(`菜单项-${text.trim()}`, 'error');
        }
      }
    }
  });

  test('07. 表格和列表功能测试', async ({ page }) => {
    // 寻找表格页面
    const tablePages = ['/tenants', '/users', '/system'];
    
    for (const pagePath of tablePages) {
      try {
        await page.goto(`${TEST_CONFIG.baseUrl}${pagePath}`);
        await helper.checkPageLoaded();

        // 测试全选功能
        const selectAll = page.locator('input[type="checkbox"][id*="all"], .select-all');
        if (await selectAll.count() > 0) {
          await helper.takeScreenshot('全选按钮', 'before');
          await selectAll.check();
          await helper.takeScreenshot('全选按钮', 'after');
        }

        // 测试分页功能
        const pagination = page.locator('.pagination a, .pager a');
        const paginationLinks = await pagination.all();
        for (let i = 0; i < Math.min(paginationLinks.length, 3); i++) {
          const link = paginationLinks[i];
          const text = await link.textContent();
          
          if (text && !text.includes('active')) {
            await helper.takeScreenshot(`分页-${text}`, 'before');
            try {
              await link.click();
              await helper.takeScreenshot(`分页-${text}`, 'after');
              await page.waitForTimeout(1000);
            } catch (error) {
              await helper.takeScreenshot(`分页-${text}`, 'error');
            }
          }
        }

        // 测试批量操作
        const batchButtons = await page.locator('.batch-actions button, .bulk-actions button').all();
        for (const btn of batchButtons) {
          const btnText = await btn.textContent();
          if (btnText) {
            await helper.takeScreenshot(`批量${btnText}按钮`, 'before');
            try {
              await btn.click();
              await helper.takeScreenshot(`批量${btnText}按钮`, 'after');
              
              // 如果有确认对话框，点击取消
              const cancelBtn = page.locator('.modal .btn-secondary, .popup .cancel');
              if (await cancelBtn.count() > 0) {
                await helper.takeScreenshot('取消按钮', 'before');
                await cancelBtn.click();
                await helper.takeScreenshot('取消按钮', 'after');
              }
            } catch (error) {
              await helper.takeScreenshot(`批量${btnText}按钮`, 'error');
            }
          }
        }

      } catch (error) {
        console.log(`页面 ${pagePath} 测试失败: ${error.message}`);
      }
    }
  });

  test('08. 表单和模态框测试', async ({ page }) => {
    // 寻找所有带有data-toggle="modal"的按钮
    const modalTriggers = await page.locator('[data-toggle="modal"], .btn-modal').all();
    
    for (let i = 0; i < Math.min(modalTriggers.length, 5); i++) {
      const trigger = modalTriggers[i];
      const triggerText = await trigger.textContent();
      
      if (triggerText) {
        await helper.takeScreenshot(`模态框触发-${triggerText}`, 'before');
        try {
          await trigger.click();
          await page.waitForTimeout(1000);
          await helper.takeScreenshot(`模态框触发-${triggerText}`, 'after');

          // 测试模态框内的表单元素
          const inputs = await page.locator('.modal input, .popup input').all();
          for (let j = 0; j < Math.min(inputs.length, 3); j++) {
            const input = inputs[j];
            const inputType = await input.getAttribute('type');
            
            if (inputType === 'text' || inputType === 'email') {
              await input.fill(`测试数据${j}`);
              await page.waitForTimeout(200);
            }
          }

          // 关闭模态框
          const closeBtn = page.locator('.modal .close, .modal .btn-secondary, .popup .close');
          if (await closeBtn.count() > 0) {
            await closeBtn.first().click();
          }
        } catch (error) {
          await helper.takeScreenshot(`模态框触发-${triggerText}`, 'error');
        }
      }
    }
  });

  test('09. 搜索和筛选功能测试', async ({ page }) => {
    // 测试搜索框
    const searchInputs = await page.locator('input[type="search"], input[placeholder*="搜索"], .search-input').all();
    
    for (let i = 0; i < searchInputs.length; i++) {
      const input = searchInputs[i];
      await helper.takeScreenshot(`搜索框${i+1}`, 'before');
      
      try {
        await input.fill('测试搜索');
        await page.waitForTimeout(500);
        
        // 寻找搜索按钮
        const searchBtn = page.locator('.search-btn, button[type="submit"]').first();
        if (await searchBtn.count() > 0) {
          await searchBtn.click();
          await helper.takeScreenshot(`搜索框${i+1}`, 'after');
        }
      } catch (error) {
        await helper.takeScreenshot(`搜索框${i+1}`, 'error');
      }
    }

    // 测试下拉筛选
    const filterSelects = await page.locator('select').all();
    for (let i = 0; i < Math.min(filterSelects.length, 3); i++) {
      const select = filterSelects[i];
      const options = await select.locator('option').all();
      
      if (options.length > 1) {
        await helper.takeScreenshot(`筛选下拉框${i+1}`, 'before');
        try {
          await select.selectOption({ index: 1 });
          await helper.takeScreenshot(`筛选下拉框${i+1}`, 'after');
          await page.waitForTimeout(500);
        } catch (error) {
          await helper.takeScreenshot(`筛选下拉框${i+1}`, 'error');
        }
      }
    }
  });

  test('10. 数据导出和操作按钮测试', async ({ page }) => {
    // 测试导出按钮
    const exportButtons = await page.locator('button:has-text("导出"), .btn-export, .export-btn').all();
    
    for (const btn of exportButtons) {
      const btnText = await btn.textContent();
      await helper.takeScreenshot(`导出数据按钮`, 'before');
      
      try {
        await btn.click();
        await helper.takeScreenshot(`导出数据按钮`, 'after');
        await page.waitForTimeout(1000);
      } catch (error) {
        await helper.takeScreenshot(`导出数据按钮`, 'error');
      }
    }

    // 测试刷新按钮
    const refreshButtons = await page.locator('button:has-text("刷新"), .btn-refresh, .refresh-btn').all();
    for (const btn of refreshButtons) {
      await helper.takeScreenshot(`刷新按钮`, 'before');
      try {
        await btn.click();
        await helper.takeScreenshot(`刷新按钮`, 'after');
        await page.waitForTimeout(1000);
      } catch (error) {
        await helper.takeScreenshot(`刷新按钮`, 'error');
      }
    }
  });

  test('11. 最终状态检查和清理', async ({ page }) => {
    // 检查所有主要页面是否可访问
    const criticalPages = [
      '/dashboard',
      '/tenants', 
      '/users',
      '/system'
    ];

    let accessiblePages = 0;
    let totalPages = criticalPages.length;

    for (const pagePath of criticalPages) {
      try {
        await page.goto(`${TEST_CONFIG.baseUrl}${pagePath}`);
        await helper.checkPageLoaded();
        accessiblePages++;
        console.log(`✅ 页面可访问: ${pagePath}`);
      } catch (error) {
        console.log(`❌ 页面不可访问: ${pagePath} - ${error.message}`);
      }
    }

    // 生成测试摘要
    const healthScore = Math.round((accessiblePages / totalPages) * 100);
    console.log(`\n=== 测试摘要 ===`);
    console.log(`可访问页面: ${accessiblePages}/${totalPages}`);
    console.log(`系统健康度: ${healthScore}%`);
    
    if (healthScore >= 90) {
      console.log(`🎉 系统状态优秀！健康度达到 ${healthScore}%`);
    } else if (healthScore >= 70) {
      console.log(`⚠️  系统状态良好，但需要改进。健康度: ${healthScore}%`);
    } else {
      console.log(`🚨 系统存在严重问题，需要立即修复。健康度: ${healthScore}%`);
    }

    // 最终截图
    await helper.takeScreenshot('最终状态检查', 'completed');

    // 断言健康度
    expect(healthScore).toBeGreaterThan(80); // 要求至少80%的页面可访问
  });
});

// 错误恢复和清理
test.afterEach(async ({ page }, testInfo) => {
  if (testInfo.status === 'failed') {
    // 失败时的额外截图
    await page.screenshot({ 
      path: `${TEST_CONFIG.screenshotPath}failure-${testInfo.title.replace(/[^a-zA-Z0-9]/g, '_')}.png`,
      fullPage: true 
    });
  }
  
  // 清理：关闭所有模态框和弹窗
  try {
    await page.locator('.modal, .popup').evaluateAll(elements => {
      elements.forEach(el => el.remove());
    });
  } catch (error) {
    // 忽略清理错误
  }
});

test.afterAll(async () => {
  console.log('\n🏁 后台管理中心全面功能审查完成');
  console.log(`📸 截图保存位置: ${TEST_CONFIG.screenshotPath}`);
  console.log('📋 请查看测试报告以获取详细结果');
});