import { test, expect } from '@playwright/test';

test.describe('SaaS Admin Interface Complete Testing', () => {
  
  test('Should load dashboard successfully', async ({ page }) => {
    await page.goto('http://localhost:4000/');
    
    // Wait for page to load completely
    await page.waitForLoadState('networkidle');
    
    // Check if we're redirected to login or dashboard loads
    const currentUrl = page.url();
    
    if (currentUrl.includes('/login')) {
      // If redirected to login, test login functionality
      await page.fill('input[name="username"]', 'super_admin');
      await page.fill('input[name="password"]', 'admin123');
      await page.click('button[type="submit"]');
      
      // Wait for redirect after login
      await page.waitForURL('**/dashboard');
    }
    
    // Now we should be on the dashboard
    await expect(page).toHaveTitle(/平台仪表盘|智慧养鹅SaaS管理平台/);
    
    // Check for key dashboard elements
    await expect(page.locator('h2')).toContainText('平台仪表盘');
  });

  test('Should navigate to tenant management', async ({ page }) => {
    await page.goto('http://localhost:4000/tenants');
    
    await page.waitForLoadState('networkidle');
    
    // Should see tenant management page
    await expect(page.locator('h2')).toContainText('租户管理');
    
    // Check for tenant list table or empty state
    const hasTable = await page.locator('table').isVisible();
    const hasEmptyState = await page.locator('.text-center').isVisible();
    
    expect(hasTable || hasEmptyState).toBeTruthy();
  });

  test('Should handle new tenant creation form', async ({ page }) => {
    await page.goto('http://localhost:4000/tenants');
    
    // Look for add tenant button
    const addButton = page.locator('button', { hasText: '新增租户' });
    if (await addButton.isVisible()) {
      await addButton.click();
      
      // Check modal appears
      await expect(page.locator('.modal')).toBeVisible();
      await expect(page.locator('.modal-title')).toContainText('新增租户');
      
      // Fill form fields
      await page.fill('input[name="name"]', 'Test Tenant');
      await page.fill('input[name="tenant_code"]', 'TEST001');
      await page.fill('input[name="contact_name"]', 'Test Contact');
      await page.fill('input[name="contact_email"]', '<EMAIL>');
      
      // Don't actually submit, just verify form works
      const submitButton = page.locator('button[type="submit"]');
      await expect(submitButton).toBeEnabled();
    }
  });

  test('Should load platform users page', async ({ page }) => {
    await page.goto('http://localhost:4000/platform-users');
    
    await page.waitForLoadState('networkidle');
    
    // Should load without error
    const hasError = await page.locator('h1', { hasText: 'Error' }).isVisible();
    expect(hasError).toBeFalsy();
  });

  test('Should load finance page', async ({ page }) => {
    await page.goto('http://localhost:4000/finance');
    
    await page.waitForLoadState('networkidle');
    
    // Should load without error
    const hasError = await page.locator('h1', { hasText: 'Error' }).isVisible();
    expect(hasError).toBeFalsy();
  });

  test('Should load settings page', async ({ page }) => {
    await page.goto('http://localhost:4000/settings');
    
    await page.waitForLoadState('networkidle');
    
    // Should load without error
    const hasError = await page.locator('h1', { hasText: 'Error' }).isVisible();
    expect(hasError).toBeFalsy();
  });

  test('Should load monitoring page', async ({ page }) => {
    await page.goto('http://localhost:4000/monitoring');
    
    await page.waitForLoadState('networkidle');
    
    // Should load without error
    const hasError = await page.locator('h1', { hasText: 'Error' }).isVisible();
    expect(hasError).toBeFalsy();
  });

  test('Should test navigation between pages', async ({ page }) => {
    await page.goto('http://localhost:4000/');
    
    // Test sidebar navigation
    const sidebarLinks = [
      { selector: 'a[href="/dashboard"]', text: '平台仪表盘' },
      { selector: 'a[href="/tenants"]', text: '租户管理' },
      { selector: 'a[href="/platform-users"]', text: '平台用户' },
      { selector: 'a[href="/finance"]', text: '财务数据汇总' },
      { selector: 'a[href="/settings"]', text: '系统设置' },
      { selector: 'a[href="/monitoring"]', text: '监控' }
    ];
    
    for (const link of sidebarLinks) {
      const linkElement = page.locator(link.selector);
      if (await linkElement.isVisible()) {
        await linkElement.click();
        await page.waitForLoadState('networkidle');
        
        // Verify no error pages
        const hasError = await page.locator('h1', { hasText: 'Error' }).isVisible();
        expect(hasError).toBeFalsy();
      }
    }
  });

  test('Should test responsive design elements', async ({ page }) => {
    await page.goto('http://localhost:4000/');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForLoadState('networkidle');
    
    // Should still be functional
    const hasContent = await page.locator('body').isVisible();
    expect(hasContent).toBeTruthy();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForLoadState('networkidle');
    
    // Should display properly
    expect(hasContent).toBeTruthy();
  });
});