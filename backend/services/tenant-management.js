/**
 * 智慧养鹅SaaS平台 - 租户管理服务
 * 完整的多租户数据管理逻辑
 */

const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

class TenantManagementService {
  constructor() {
    this.platformDbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.PLATFORM_DB_USER || 'saas_admin',
      password: process.env.PLATFORM_DB_PASSWORD || 'saas_admin_2024!',
      database: process.env.PLATFORM_DB_NAME || 'smart_goose_saas_platform',
      charset: 'utf8mb4',
      connectionLimit: 10
    };
    
    this.platformPool = mysql.createPool(this.platformDbConfig);
    this.tenantPools = new Map(); // 缓存租户数据库连接池
  }

  // 获取租户数据库连接池
  async getTenantPool(tenantCode) {
    if (!this.tenantPools.has(tenantCode)) {
      try {
        const [tenantRows] = await this.platformPool.execute(
          'SELECT database_name FROM tenants WHERE tenant_code = ? AND status = "active"',
          [tenantCode]
        );
        
        if (tenantRows.length === 0) {
          throw new Error(`租户 ${tenantCode} 不存在或未激活`);
        }

        const tenantConfig = {
          ...this.platformDbConfig,
          database: tenantRows[0].database_name,
          user: `tenant_${tenantCode.toLowerCase()}`,
          password: `${tenantCode.toLowerCase()}_2024!`
        };

        const pool = mysql.createPool(tenantConfig);
        this.tenantPools.set(tenantCode, pool);
      } catch (error) {
        throw new Error(`无法连接租户 ${tenantCode} 数据库: ${error.message}`);
      }
    }
    
    return this.tenantPools.get(tenantCode);
  }

  // 创建新租户
  async createTenant(tenantData) {
    const connection = await this.platformPool.getConnection();
    await connection.beginTransaction();

    try {
      const {
        name,
        displayName,
        contactName,
        contactEmail,
        contactPhone,
        address,
        subscriptionPlan = 'trial',
        maxUsers,
        maxFarms,
        maxFlocks,
        customSettings = {}
      } = tenantData;

      // 生成租户代码
      const tenantCode = this.generateTenantCode(name);
      const databaseName = `tenant_${tenantCode.toLowerCase()}_db`;
      
      // 检查租户代码是否已存在
      const [existingTenant] = await connection.execute(
        'SELECT id FROM tenants WHERE tenant_code = ?',
        [tenantCode]
      );
      
      if (existingTenant.length > 0) {
        throw new Error('租户代码已存在');
      }

      // 获取订阅计划信息
      const [planRows] = await connection.execute(
        'SELECT * FROM subscription_plans WHERE plan_code = ? AND is_active = true',
        [subscriptionPlan]
      );
      
      if (planRows.length === 0) {
        throw new Error('无效的订阅计划');
      }

      const plan = planRows[0];
      const subscriptionEndDate = new Date();
      subscriptionEndDate.setDate(subscriptionEndDate.getDate() + (subscriptionPlan === 'trial' ? 7 : 30));

      // 插入租户记录
      const [tenantResult] = await connection.execute(`
        INSERT INTO tenants (
          tenant_code, name, display_name, database_name, contact_name, contact_email, contact_phone, address,
          subscription_plan, subscription_status, subscription_start_date, subscription_end_date,
          max_users, max_farms, max_flocks, storage_quota, api_quota, custom_settings, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', CURDATE(), ?, ?, ?, ?, ?, ?, ?, 'active')
      `, [
        tenantCode, name, displayName, databaseName, contactName, contactEmail, contactPhone, address,
        subscriptionPlan, subscriptionEndDate,
        maxUsers || plan.max_users, maxFarms || plan.max_farms, maxFlocks || plan.max_flocks,
        plan.storage_quota, plan.api_quota, JSON.stringify(customSettings)
      ]);

      const tenantId = tenantResult.insertId;

      // 创建租户管理员账户
      const adminPassword = await bcrypt.hash('admin123', 10);
      await connection.execute(`
        INSERT INTO tenant_users (tenant_id, username, email, password, name, role, status)
        VALUES (?, 'admin', ?, ?, ?, 'owner', 'active')
      `, [
        tenantId,
        contactEmail || `admin@${tenantCode.toLowerCase()}.com`,
        adminPassword,
        contactName || '系统管理员'
      ]);

      // 记录操作日志
      await connection.execute(`
        INSERT INTO platform_logs (tenant_id, action, module, details, status)
        VALUES (?, 'create_tenant', 'tenant_management', ?, 'success')
      `, [tenantId, JSON.stringify({ tenant_code: tenantCode, subscription_plan: subscriptionPlan })]);

      await connection.commit();

      // 异步创建租户数据库
      this.createTenantDatabaseAsync(tenantCode).catch(error => {
        console.error(`创建租户 ${tenantCode} 数据库失败:`, error.message);
      });

      return {
        id: tenantId,
        tenantCode,
        databaseName,
        message: '租户创建成功，数据库正在后台初始化'
      };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 异步创建租户数据库
  async createTenantDatabaseAsync(tenantCode) {
    const rootConnection = await mysql.createConnection({
      host: this.platformDbConfig.host,
      port: this.platformDbConfig.port,
      user: 'root',
      password: process.env.MYSQL_ROOT_PASSWORD || '',
      charset: 'utf8mb4'
    });

    try {
      const dbName = `tenant_${tenantCode.toLowerCase()}_db`;
      const dbUser = `tenant_${tenantCode.toLowerCase()}`;
      const dbPassword = `${tenantCode.toLowerCase()}_2024!`;

      // 创建数据库和用户
      await rootConnection.execute(`CREATE DATABASE IF NOT EXISTS ${dbName} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
      await rootConnection.execute(`CREATE USER IF NOT EXISTS '${dbUser}'@'localhost' IDENTIFIED BY '${dbPassword}'`);
      await rootConnection.execute(`GRANT ALL PRIVILEGES ON ${dbName}.* TO '${dbUser}'@'localhost'`);
      await rootConnection.execute(`FLUSH PRIVILEGES`);

      // 创建租户业务表
      const tenantConnection = await mysql.createConnection({
        ...this.platformDbConfig,
        database: dbName,
        user: 'root',
        password: process.env.MYSQL_ROOT_PASSWORD || ''
      });

      await this.createTenantBusinessTables(tenantConnection);
      await tenantConnection.end();

      // 更新租户状态
      await this.platformPool.execute(
        'UPDATE tenants SET status = "active" WHERE tenant_code = ?',
        [tenantCode]
      );

      console.log(`✅ 租户 ${tenantCode} 数据库创建完成`);

    } catch (error) {
      console.error(`❌ 创建租户 ${tenantCode} 数据库失败:`, error.message);
      
      // 更新租户状态为失败
      await this.platformPool.execute(
        'UPDATE tenants SET status = "inactive" WHERE tenant_code = ?',
        [tenantCode]
      );
    } finally {
      await rootConnection.end();
    }
  }

  // 创建租户业务表
  async createTenantBusinessTables(connection) {
    const tables = [
      // 用户表
      `CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100),
        phone VARCHAR(20),
        name VARCHAR(100),
        role ENUM('admin', 'manager', 'operator', 'viewer') DEFAULT 'viewer',
        department VARCHAR(100),
        avatar VARCHAR(255),
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_username (username),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 养殖场表
      `CREATE TABLE IF NOT EXISTS farms (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        address TEXT,
        area DECIMAL(10,2) COMMENT '面积(平方米)',
        capacity INT COMMENT '容量(只)',
        manager_id INT,
        contact_phone VARCHAR(20),
        coordinates JSON COMMENT 'GPS坐标',
        status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`,

      // 鹅群表
      `CREATE TABLE IF NOT EXISTS flocks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        farm_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        breed VARCHAR(50) COMMENT '品种',
        count INT DEFAULT 0 COMMENT '数量',
        birth_date DATE COMMENT '出生日期',
        source VARCHAR(100) COMMENT '来源',
        status ENUM('healthy', 'sick', 'quarantine', 'sold') DEFAULT 'healthy',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (farm_id) REFERENCES farms(id) ON DELETE CASCADE,
        INDEX idx_farm_status (farm_id, status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci`
    ];

    for (const sql of tables) {
      await connection.execute(sql);
    }

    // 插入初始管理员用户
    const adminPassword = await bcrypt.hash('admin123', 10);
    await connection.execute(
      'INSERT INTO users (username, password, email, name, role, status) VALUES (?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP',
      ['admin', adminPassword, '<EMAIL>', '系统管理员', 'admin', 'active']
    );
  }

  // 获取租户列表
  async getTenants(options = {}) {
    const { page = 1, limit = 20, status, subscriptionPlan, search } = options;
    const offset = (page - 1) * limit;
    
    let whereClause = '1=1';
    let params = [];
    
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }
    
    if (subscriptionPlan) {
      whereClause += ' AND subscription_plan = ?';
      params.push(subscriptionPlan);
    }
    
    if (search) {
      whereClause += ' AND (name LIKE ? OR tenant_code LIKE ? OR contact_email LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    const [countResult] = await this.platformPool.execute(
      `SELECT COUNT(*) as total FROM tenants WHERE ${whereClause}`,
      params
    );

    const [tenants] = await this.platformPool.execute(`
      SELECT 
        t.id, t.tenant_code, t.name, t.display_name, t.contact_name, t.contact_email,
        t.subscription_plan, t.subscription_status, t.subscription_end_date,
        t.max_users, t.max_farms, t.max_flocks, t.status, t.created_at,
        sp.display_name as plan_display_name, sp.price,
        (SELECT COUNT(*) FROM tenant_users tu WHERE tu.tenant_id = t.id AND tu.status = 'active') as user_count,
        (SELECT COUNT(*) FROM usage_stats us WHERE us.tenant_id = t.id AND us.stat_type = 'active_users' AND us.stat_date = CURDATE()) as active_users_today
      FROM tenants t
      LEFT JOIN subscription_plans sp ON t.subscription_plan = sp.plan_code
      WHERE ${whereClause}
      ORDER BY t.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);

    return {
      data: tenants,
      pagination: {
        page,
        limit,
        total: countResult[0].total,
        pages: Math.ceil(countResult[0].total / limit)
      }
    };
  }

  // 获取租户详情
  async getTenantById(tenantId) {
    const [tenants] = await this.platformPool.execute(`
      SELECT 
        t.*,
        sp.display_name as plan_display_name, sp.price, sp.features,
        (SELECT COUNT(*) FROM tenant_users tu WHERE tu.tenant_id = t.id AND tu.status = 'active') as user_count
      FROM tenants t
      LEFT JOIN subscription_plans sp ON t.subscription_plan = sp.plan_code
      WHERE t.id = ?
    `, [tenantId]);

    if (tenants.length === 0) {
      throw new Error('租户不存在');
    }

    const tenant = tenants[0];

    // 获取使用统计
    const [usageStats] = await this.platformPool.execute(`
      SELECT stat_type, stat_value, stat_date 
      FROM usage_stats 
      WHERE tenant_id = ? AND stat_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
      ORDER BY stat_date DESC, stat_type
    `, [tenantId]);

    // 获取最近的支付记录
    const [payments] = await this.platformPool.execute(`
      SELECT amount, payment_status, paid_at, billing_period_start, billing_period_end
      FROM payments 
      WHERE tenant_id = ? 
      ORDER BY created_at DESC 
      LIMIT 5
    `, [tenantId]);

    return {
      ...tenant,
      custom_settings: JSON.parse(tenant.custom_settings || '{}'),
      usage_stats: usageStats,
      recent_payments: payments
    };
  }

  // 更新租户信息
  async updateTenant(tenantId, updateData) {
    const connection = await this.platformPool.getConnection();
    await connection.beginTransaction();

    try {
      const allowedFields = [
        'name', 'display_name', 'contact_name', 'contact_email', 'contact_phone', 'address',
        'max_users', 'max_farms', 'max_flocks', 'custom_settings', 'status'
      ];

      const updates = [];
      const params = [];

      Object.keys(updateData).forEach(key => {
        if (allowedFields.includes(key) && updateData[key] !== undefined) {
          updates.push(`${key} = ?`);
          params.push(key === 'custom_settings' ? JSON.stringify(updateData[key]) : updateData[key]);
        }
      });

      if (updates.length === 0) {
        throw new Error('没有有效的更新字段');
      }

      params.push(tenantId);

      await connection.execute(`
        UPDATE tenants 
        SET ${updates.join(', ')}, updated_at = CURRENT_TIMESTAMP 
        WHERE id = ?
      `, params);

      // 记录操作日志
      await connection.execute(`
        INSERT INTO platform_logs (tenant_id, action, module, details, status)
        VALUES (?, 'update_tenant', 'tenant_management', ?, 'success')
      `, [tenantId, JSON.stringify(updateData)]);

      await connection.commit();

      return await this.getTenantById(tenantId);

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 删除租户 (软删除)
  async deleteTenant(tenantId) {
    const connection = await this.platformPool.getConnection();
    await connection.beginTransaction();

    try {
      // 获取租户信息
      const [tenants] = await connection.execute(
        'SELECT tenant_code, database_name FROM tenants WHERE id = ?',
        [tenantId]
      );

      if (tenants.length === 0) {
        throw new Error('租户不存在');
      }

      const tenant = tenants[0];

      // 软删除租户
      await connection.execute(
        'UPDATE tenants SET status = "deleted", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [tenantId]
      );

      // 禁用租户用户
      await connection.execute(
        'UPDATE tenant_users SET status = "inactive" WHERE tenant_id = ?',
        [tenantId]
      );

      // 记录操作日志
      await connection.execute(`
        INSERT INTO platform_logs (tenant_id, action, module, details, status)
        VALUES (?, 'delete_tenant', 'tenant_management', ?, 'success')
      `, [tenantId, JSON.stringify({ tenant_code: tenant.tenant_code })]);

      await connection.commit();

      // 清理连接池缓存
      if (this.tenantPools.has(tenant.tenant_code)) {
        await this.tenantPools.get(tenant.tenant_code).end();
        this.tenantPools.delete(tenant.tenant_code);
      }

      return { message: '租户删除成功' };

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // 租户数据操作接口
  async executeQuery(tenantCode, sql, params = []) {
    const tenantPool = await this.getTenantPool(tenantCode);
    return await tenantPool.execute(sql, params);
  }

  // 获取租户统计数据
  async getTenantStats(tenantCode) {
    const tenantPool = await this.getTenantPool(tenantCode);
    
    const stats = {};

    // 用户统计
    const [userStats] = await tenantPool.execute(
      'SELECT COUNT(*) as total, SUM(status = "active") as active FROM users'
    );
    stats.users = userStats[0];

    // 养殖场统计
    const [farmStats] = await tenantPool.execute(
      'SELECT COUNT(*) as total, SUM(status = "active") as active FROM farms'
    );
    stats.farms = farmStats[0];

    // 鹅群统计
    const [flockStats] = await tenantPool.execute(
      'SELECT COUNT(*) as total, SUM(count) as total_goose_count, SUM(status = "healthy") as healthy FROM flocks'
    );
    stats.flocks = flockStats[0];

    // 今日记录统计
    const [recordStats] = await tenantPool.execute(`
      SELECT 
        (SELECT COUNT(*) FROM health_records WHERE DATE(created_at) = CURDATE()) as health_records,
        (SELECT COUNT(*) FROM production_records WHERE DATE(created_at) = CURDATE()) as production_records
    `);
    stats.today_records = recordStats[0];

    return stats;
  }

  // 生成租户代码
  generateTenantCode(name) {
    const prefix = name.substring(0, 3).toUpperCase();
    const timestamp = Date.now().toString().slice(-6);
    return `${prefix}${timestamp}`;
  }

  // 生成API密钥
  async generateApiKey(tenantId, keyName, permissions = {}) {
    const apiKey = 'sk_' + crypto.randomBytes(24).toString('hex');
    const apiSecret = crypto.randomBytes(32).toString('hex');

    const [result] = await this.platformPool.execute(`
      INSERT INTO api_keys (tenant_id, key_name, api_key, api_secret, permissions, is_active)
      VALUES (?, ?, ?, ?, ?, true)
    `, [tenantId, keyName, apiKey, apiSecret, JSON.stringify(permissions)]);

    return {
      id: result.insertId,
      api_key: apiKey,
      api_secret: apiSecret,
      message: 'API密钥生成成功，请妥善保管'
    };
  }

  // 验证API密钥
  async validateApiKey(apiKey) {
    const [keys] = await this.platformPool.execute(`
      SELECT ak.*, t.tenant_code, t.status as tenant_status
      FROM api_keys ak
      JOIN tenants t ON ak.tenant_id = t.id
      WHERE ak.api_key = ? AND ak.is_active = true AND (ak.expires_at IS NULL OR ak.expires_at > NOW())
    `, [apiKey]);

    if (keys.length === 0) {
      return null;
    }

    const key = keys[0];
    
    if (key.tenant_status !== 'active') {
      return null;
    }

    // 更新使用统计
    await this.platformPool.execute(
      'UPDATE api_keys SET last_used_at = NOW(), usage_count = usage_count + 1 WHERE id = ?',
      [key.id]
    );

    return {
      tenant_id: key.tenant_id,
      tenant_code: key.tenant_code,
      permissions: JSON.parse(key.permissions || '{}'),
      rate_limit: key.rate_limit
    };
  }

  // 关闭所有连接
  async close() {
    await this.platformPool.end();
    
    for (const [tenantCode, pool] of this.tenantPools) {
      await pool.end();
    }
    
    this.tenantPools.clear();
  }
}

module.exports = TenantManagementService;