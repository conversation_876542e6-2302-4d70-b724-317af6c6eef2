<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%- title || '智慧养鹅SaaS管理平台' %></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #faad14;
            --error-color: #ff4d4f;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            background-color: #f0f2f5;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin: 0.2rem 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }

        .sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 20px;
        }

        .main-content {
            padding: 2rem;
        }

        .stat-card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .card-body {
            padding: 1.5rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .bg-primary-light { background-color: rgba(24, 144, 255, 0.1); color: var(--primary-color); }
        .bg-success-light { background-color: rgba(82, 196, 26, 0.1); color: var(--success-color); }
        .bg-warning-light { background-color: rgba(250, 173, 20, 0.1); color: var(--warning-color); }
        .bg-info-light { background-color: rgba(24, 144, 255, 0.1); color: var(--primary-color); }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-2 sidebar">
                <div class="p-3">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-feather-alt me-2"></i>
                        智慧养鹅SaaS
                    </h4>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="/dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                平台仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/tenants">
                                <i class="fas fa-building"></i>
                                租户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/platform-users">
                                <i class="fas fa-users"></i>
                                平台用户
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/finance">
                                <i class="fas fa-chart-line"></i>
                                财务数据汇总
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">
                                <i class="fas fa-cog"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/monitoring">
                                <i class="fas fa-chart-bar"></i>
                                监控
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main Content -->
            <main class="col-md-10 ms-sm-auto main-content">
                <!-- Top Navigation -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white mb-4" style="border-radius: 16px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <div class="container-fluid">
                        <span class="navbar-brand"><%- title %></span>
                        
                        <div class="navbar-nav ms-auto">
                            <div class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user-circle"></i>
                                    <%= (typeof user !== 'undefined' && user && user.username) ? user.username : 'admin' %>
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form action="/logout" method="POST" style="display: inline;">
                                            <button type="submit" class="dropdown-item">退出登录</button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </nav>

                <!-- Page Content -->
                <%- body %>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>