<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .price-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }
        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #6c757d; }
        .price-history-card {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2><i class="fas fa-chart-line text-warning"></i> 今日鹅价管理</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <% breadcrumb.forEach((item, index) => { %>
                            <% if (index === breadcrumb.length - 1) { %>
                                <li class="breadcrumb-item active"><%= item.name %></li>
                            <% } else { %>
                                <li class="breadcrumb-item"><a href="<%= item.url %>"><%= item.name %></a></li>
                            <% } %>
                        <% }); %>
                    </ol>
                </nav>
            </div>
            <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#publishPriceModal">
                <i class="fas fa-plus"></i> 发布今日鹅价
            </button>
        </div>

        <!-- 今日鹅价概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="price-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2>¥<%= (stats.today_price || 28.5).toFixed(1) %></h2>
                            <p class="mb-0">今日鹅价 (元/斤)</p>
                        </div>
                        <i class="fas fa-feather-alt fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="<%= stats.price_change >= 0 ? 'trend-up' : 'trend-down' %>">
                                    <%= stats.price_change >= 0 ? '+' : '' %><%= (stats.price_change || 0.8).toFixed(2) %>
                                </h4>
                                <p class="mb-0">较昨日变化</p>
                            </div>
                            <i class="fas fa-<%= stats.price_change >= 0 ? 'arrow-up' : 'arrow-down' %> fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><%= stats.total_regions || 5 %></h4>
                                <p class="mb-0">覆盖地区</p>
                            </div>
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><%= stats.total_breeds || 3 %></h4>
                                <p class="mb-0">鹅品种</p>
                            </div>
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- 价格趋势图 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-area"></i> 价格趋势 (最近30天)</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="priceChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <!-- 最新价格列表 -->
            <div class="col-md-4">
                <div class="card price-history-card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> 最新价格记录</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <% if (latestPrices && latestPrices.length > 0) { %>
                                <% latestPrices.slice(0, 10).forEach(price => { %>
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">¥<%= price.price_per_kg %>/斤</h6>
                                                <small class="text-muted">
                                                    <%= price.region %> · <%= price.breed_type %>
                                                </small>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">
                                                    <%= new Date(price.price_date).toLocaleDateString() %>
                                                </small>
                                                <br>
                                                <span class="badge bg-<%= price.market_trend === 'up' ? 'success' : price.market_trend === 'down' ? 'danger' : 'secondary' %>">
                                                    <%= price.market_trend === 'up' ? '上涨' : price.market_trend === 'down' ? '下跌' : '稳定' %>
                                                </span>
                                            </div>
                                        </div>
                                        <% if (price.notes) { %>
                                            <p class="mb-1 mt-2"><small><%= price.notes %></small></p>
                                        <% } %>
                                    </div>
                                <% }); %>
                            <% } else { %>
                                <div class="list-group-item text-center">
                                    <i class="fas fa-info-circle text-muted"></i>
                                    <p class="mb-0 text-muted">暂无价格记录</p>
                                </div>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细价格管理表格 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> 价格管理</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>价格 (元/斤)</th>
                                <th>地区</th>
                                <th>品种</th>
                                <th>市场趋势</th>
                                <th>发布人</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% if (latestPrices && latestPrices.length > 0) { %>
                                <% latestPrices.forEach(price => { %>
                                    <tr>
                                        <td><%= new Date(price.price_date).toLocaleDateString() %></td>
                                        <td><strong>¥<%= price.price_per_kg %></strong></td>
                                        <td><%= price.region %></td>
                                        <td><%= price.breed_type %></td>
                                        <td>
                                            <span class="badge bg-<%= price.market_trend === 'up' ? 'success' : price.market_trend === 'down' ? 'danger' : 'secondary' %>">
                                                <%= price.market_trend === 'up' ? '上涨' : price.market_trend === 'down' ? '下跌' : '稳定' %>
                                            </span>
                                        </td>
                                        <td><%= price.publisher_name || '系统' %></td>
                                        <td><%= price.notes || '-' %></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary" onclick="editPrice('<%= price.id %>')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <% }); %>
                            <% } else { %>
                                <tr>
                                    <td colspan="8" class="text-center text-muted">暂无价格数据</td>
                                </tr>
                            <% } %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 发布鹅价模态框 -->
    <div class="modal fade" id="publishPriceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">发布今日鹅价</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="publishPriceForm">
                        <div class="mb-3">
                            <label class="form-label">价格 (元/斤) *</label>
                            <input type="number" class="form-control" name="price_per_kg" step="0.1" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">地区 *</label>
                            <select class="form-select" name="region" required>
                                <option value="">请选择地区</option>
                                <option value="华东">华东</option>
                                <option value="华南">华南</option>
                                <option value="华北">华北</option>
                                <option value="华中">华中</option>
                                <option value="西南">西南</option>
                                <option value="西北">西北</option>
                                <option value="东北">东北</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">鹅品种 *</label>
                            <select class="form-select" name="breed_type" required>
                                <option value="">请选择品种</option>
                                <option value="白鹅">白鹅</option>
                                <option value="灰鹅">灰鹅</option>
                                <option value="狮头鹅">狮头鹅</option>
                                <option value="太湖鹅">太湖鹅</option>
                                <option value="四川白鹅">四川白鹅</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">市场趋势 *</label>
                            <select class="form-select" name="market_trend" required>
                                <option value="">请选择趋势</option>
                                <option value="up">上涨</option>
                                <option value="down">下跌</option>
                                <option value="stable">稳定</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" name="notes" rows="3" placeholder="价格变动原因、市场分析等..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="submitPublishPrice()">发布价格</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 价格趋势图
        const ctx = document.getElementById('priceChart').getContext('2d');
        const trendData = <%- JSON.stringify(trendData || []) %>;
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: trendData.map(d => new Date(d.price_date).toLocaleDateString()),
                datasets: [{
                    label: '平均价格 (元/斤)',
                    data: trendData.map(d => d.avg_price),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: '价格 (元/斤)'
                        }
                    }
                }
            }
        });

        function editPrice(priceId) {
            alert('编辑价格功能开发中... ID: ' + priceId);
        }

        function submitPublishPrice() {
            const form = document.getElementById('publishPriceForm');
            const formData = new FormData(form);
            
            // TODO: 实现发布价格的AJAX请求
            alert('发布价格功能开发中...');
        }
    </script>
</body>
</html>