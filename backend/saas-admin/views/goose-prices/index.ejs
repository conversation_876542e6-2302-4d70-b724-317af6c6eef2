<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-chart-line me-2"></i>鹅价格监控</h2>
                <div>
                    <a href="/goose-prices/create" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>添加价格记录
                    </a>
                    <a href="/goose-prices/trends" class="btn btn-info ms-2">
                        <i class="fas fa-chart-area me-1"></i>价格趋势
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div>
                            <h5 class="card-title">当前平均价格</h5>
                            <h3 class="mb-0">¥<span id="currentPrice"><%= stats.currentPrice || 0 %></span>/斤</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-yen-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">本月涨幅</h5>
                            <h3 class="mb-0">
                                <% if (stats.monthlyChange >= 0) { %>
                                    <i class="fas fa-arrow-up me-1"></i>+<%= stats.monthlyChange || 0 %>%
                                <% } else { %>
                                    <i class="fas fa-arrow-down me-1"></i><%= stats.monthlyChange || 0 %>%
                                <% } %>
                            </h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-trending-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">最高价格</h5>
                            <h3 class="mb-0">¥<%= stats.maxPrice || 0 %>/斤</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <h5 class="card-title">最低价格</h5>
                            <h3 class="mb-0">¥<%= stats.minPrice || 0 %>/斤</h3>
                        </div>
                        <div class="ms-3">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 价格趋势图表 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line me-2"></i>价格趋势图</h5>
                </div>
                <div class="card-body">
                    <canvas id="priceChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 价格记录表格 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-table me-2"></i>价格记录</h5>
                    <div class="d-flex">
                        <select class="form-select me-2" id="regionFilter">
                            <option value="">全部地区</option>
                            <% regions.forEach(region => { %>
                                <option value="<%= region %>"><%= region %></option>
                            <% }) %>
                        </select>
                        <input type="date" class="form-control" id="dateFilter" placeholder="选择日期">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="priceTable">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>地区</th>
                                    <th>市场名称</th>
                                    <th>鹅种类型</th>
                                    <th>价格(元/斤)</th>
                                    <th>价格变化</th>
                                    <th>数据来源</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% if (prices && prices.length > 0) { %>
                                    <% prices.forEach(price => { %>
                                        <tr>
                                            <td><%= Utils.formatDate(price.record_date, 'YYYY-MM-DD') %></td>
                                            <td><%= price.region %></td>
                                            <td><%= price.market_name %></td>
                                            <td><%= price.goose_type %></td>
                                            <td>
                                                <span class="fw-bold text-primary">¥<%= Utils.formatNumber(price.price_per_jin, 2) %></span>
                                            </td>
                                            <td>
                                                <% if (price.price_change > 0) { %>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-arrow-up me-1"></i>+<%= Utils.formatNumber(price.price_change, 2) %>%
                                                    </span>
                                                <% } else if (price.price_change < 0) { %>
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-arrow-down me-1"></i><%= Utils.formatNumber(price.price_change, 2) %>%
                                                    </span>
                                                <% } else { %>
                                                    <span class="badge bg-secondary">
                                                        <i class="fas fa-minus me-1"></i>0%
                                                    </span>
                                                <% } %>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><%= price.data_source %></span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="/goose-prices/edit/<%= price.id %>" class="btn btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button class="btn btn-outline-danger" onclick="deletePrice(<%= price.id %>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <% }) %>
                                <% } else { %>
                                    <tr>
                                        <td colspan="8" class="text-center py-4 text-muted">
                                            <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                            暂无价格记录
                                        </td>
                                    </tr>
                                <% } %>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 初始化价格趋势图
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('priceChart').getContext('2d');
    const chartData = <%- JSON.stringify(chartData || {labels: [], datasets: []}) %>;
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels || [],
            datasets: [{
                label: '平均价格(元/斤)',
                data: chartData.prices || [],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return '¥' + value;
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '价格: ¥' + context.parsed.y + '/斤';
                        }
                    }
                }
            }
        }
    });

    // 筛选功能
    const regionFilter = document.getElementById('regionFilter');
    const dateFilter = document.getElementById('dateFilter');
    
    regionFilter.addEventListener('change', filterTable);
    dateFilter.addEventListener('change', filterTable);
    
    function filterTable() {
        const region = regionFilter.value;
        const date = dateFilter.value;
        
        // 这里可以添加AJAX请求来重新加载数据
        window.location.search = `?region=${region}&date=${date}`;
    }
});

// 删除价格记录
function deletePrice(id) {
    if (confirm('确定要删除这条价格记录吗？')) {
        fetch(`/goose-prices/delete/${id}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败，请重试');
        });
    }
}
</script>