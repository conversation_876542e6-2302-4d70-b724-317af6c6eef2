<!-- SAAS平台核心统计卡片 -->
<div class="row">
  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-info">
      <div class="inner">
        <h3>
          <%= stats.totalTenants %>
        </h3>
        <p>总租户数</p>
      </div>
      <div class="icon">
        <i class="fas fa-building"></i>
      </div>
      <a href="/tenants" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-success">
      <div class="inner">
        <h3>
          <%= stats.activeTenants %>
        </h3>
        <p>活跃租户</p>
      </div>
      <div class="icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <a href="/tenants?status=active" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-warning">
      <div class="inner">
        <h3>
          <%= stats.totalUsers %>
        </h3>
        <p>平台用户总数</p>
      </div>
      <div class="icon">
        <i class="fas fa-users"></i>
      </div>
      <a href="/platform-users" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-danger">
      <div class="inner">
        <h3>¥<%= (stats.monthlyRevenue || 0).toLocaleString() %>
        </h3>
        <p>本月收入</p>
      </div>
      <div class="icon">
        <i class="fas fa-dollar-sign"></i>
      </div>
      <a href="/mall/orders" class="small-box-footer">查看详情 <i class="fas fa-arrow-circle-right"></i></a>
    </div>
  </div>
</div>

<!-- 订阅计划统计 -->
<div class="row">
  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-secondary">
      <div class="inner">
        <h3>
          <%= stats.basicPlan %>
        </h3>
        <p>基础版租户</p>
      </div>
      <div class="icon">
        <i class="fas fa-layer-group"></i>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-primary">
      <div class="inner">
        <h3>
          <%= stats.standardPlan %>
        </h3>
        <p>标准版租户</p>
      </div>
      <div class="icon">
        <i class="fas fa-star"></i>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-warning">
      <div class="inner">
        <h3>
          <%= stats.premiumPlan %>
        </h3>
        <p>高级版租户</p>
      </div>
      <div class="icon">
        <i class="fas fa-crown"></i>
      </div>
    </div>
  </div>

  <div class="col-lg-3 col-md-6">
    <div class="small-box bg-success">
      <div class="inner">
        <h3>
          <%= stats.enterprisePlan %>
        </h3>
        <p>企业版租户</p>
      </div>
      <div class="icon">
        <i class="fas fa-trophy"></i>
      </div>
    </div>
  </div>
</div>

<!-- 业务数据统计 -->
<div class="row business-stats">
  <div class="col-lg-2 col-md-4 col-6">
    <div class="info-box">
      <span class="info-box-icon bg-info"><i class="fas fa-egg"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">鹅群总数</span>
        <span class="info-box-number">
          <%= stats.totalFlocks %>
        </span>
      </div>
    </div>
  </div>

  <div class="col-lg-2 col-md-4 col-6">
    <div class="info-box">
      <span class="info-box-icon bg-success"><i class="fas fa-feather-alt"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">鹅只总数</span>
        <span class="info-box-number">
          <%= stats.totalGeese || 0 %>
        </span>
      </div>
    </div>
  </div>

  <div class="col-lg-2 col-md-4 col-6">
    <div class="info-box">
      <span class="info-box-icon bg-warning"><i class="fas fa-chart-line"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">今日记录</span>
        <span class="info-box-number">
          <%= stats.todayRecords %>
        </span>
      </div>
    </div>
  </div>

  <div class="col-lg-2 col-md-4 col-6">
    <div class="info-box">
      <span class="info-box-icon bg-danger"><i class="fas fa-egg"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">今日产蛋</span>
        <span class="info-box-number">
          <%= stats.todayEggs || 0 %>
        </span>
      </div>
    </div>
  </div>

  <div class="col-lg-2 col-md-4 col-6">
    <div class="info-box">
      <span class="info-box-icon bg-purple"><i class="fas fa-shopping-cart"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">商品总数</span>
        <span class="info-box-number">
          <%= stats.activeProducts %>
        </span>
      </div>
    </div>
  </div>

  <div class="col-lg-2 col-md-4 col-6">
    <div class="info-box">
      <span class="info-box-icon bg-teal"><i class="fas fa-coins"></i></span>
      <div class="info-box-content">
        <span class="info-box-text">今日鹅价</span>
        <span class="info-box-number">
          <%= stats.publishedPrices %>
        </span>
      </div>
    </div>
  </div>
</div>

<!-- 主要内容区域 -->
<div class="row">
  <!-- 系统健康状态 -->
  <div class="col-lg-8">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-server me-2"></i>
          系统状态监控
        </h3>
        <div class="card-tools">
          <button type="button" class="btn btn-tool" onclick="location.reload()">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>
      </div>
      <div class="card-body system-monitoring">
        <div class="row">
          <div class="col-md-3 col-sm-6 col-12">
            <div
              class="info-box <%= systemHealth.database.status === 'healthy' ? 'status-healthy' : 'status-danger' %>">
              <span
                class="info-box-icon <%= systemHealth.database.status === 'healthy' ? 'bg-success' : 'bg-danger' %>">
                <i class="fas fa-database"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">数据库</span>
                <span class="info-box-number">
                  <%= systemHealth.database.status==='healthy' ? '正常' : '异常' %>
                </span>
                <div class="progress">
                  <div
                    class="progress-bar <%= systemHealth.database.status === 'healthy' ? 'bg-success' : 'bg-danger' %>"
                    style="width: <%= systemHealth.database.status === 'healthy' ? '100' : '0' %>%"></div>
                </div>
                <span class="progress-description">
                  响应时间: <%= systemHealth.database.responseTime || 0 %>ms
                </span>
              </div>
            </div>
          </div>

          <div class="col-md-3 col-sm-6 col-12">
            <div class="info-box <%= systemHealth.expiringSubs > 0 ? 'status-warning' : 'status-healthy' %>">
              <span class="info-box-icon <%= systemHealth.expiringSubs > 0 ? 'bg-warning' : 'bg-success' %>">
                <i class="fas fa-calendar-times"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">即将到期</span>
                <span class="info-box-number">
                  <%= systemHealth.expiringSubs || 0 %>
                </span>
                <div class="progress">
                  <div class="progress-bar bg-warning"
                    style="width: <%= Math.min((systemHealth.expiringSubs || 0) * 10, 100) %>%"></div>
                </div>
                <span class="progress-description">
                  30天内到期订阅
                </span>
              </div>
            </div>
          </div>

          <div class="col-md-3 col-sm-6 col-12">
            <div class="info-box <%= systemHealth.pendingOrders > 0 ? 'status-warning' : 'status-healthy' %>">
              <span class="info-box-icon <%= systemHealth.pendingOrders > 0 ? 'bg-warning' : 'bg-success' %>">
                <i class="fas fa-clock"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">待处理订单</span>
                <span class="info-box-number">
                  <%= systemHealth.pendingOrders || 0 %>
                </span>
                <div class="progress">
                  <div class="progress-bar bg-warning"
                    style="width: <%= Math.min((systemHealth.pendingOrders || 0) * 5, 100) %>%"></div>
                </div>
                <span class="progress-description">
                  需要处理的订单
                </span>
              </div>
            </div>
          </div>

          <div class="col-md-3 col-sm-6 col-12">
            <div class="info-box status-info">
              <span class="info-box-icon bg-info">
                <i class="fas fa-plug"></i>
              </span>
              <div class="info-box-content">
                <span class="info-box-text">API端点</span>
                <span class="info-box-number">
                  <%= (systemHealth.apiEndpoints && systemHealth.apiEndpoints.active) || 0 %>/<%=
                      (systemHealth.apiEndpoints && systemHealth.apiEndpoints.total) || 0 %>
                </span>
                <div class="progress">
                  <div class="progress-bar bg-info"
                    style="width: <%= (systemHealth.apiEndpoints && systemHealth.apiEndpoints.total > 0) ? (systemHealth.apiEndpoints.active / systemHealth.apiEndpoints.total * 100) : 0 %>%">
                  </div>
                </div>
                <span class="progress-description">
                  活跃端点数
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统资源使用情况 -->
        <div class="row mt-3">
          <div class="col-md-4">
            <div class="progress-group">
              CPU 使用率
              <span class="float-right"><b>
                  <%= Math.round(systemHealth.systemLoad.cpu) %>
                </b>/100%</span>
              <div class="progress progress-sm">
                <div
                  class="progress-bar <%= systemHealth.systemLoad.cpu > 80 ? 'bg-danger' : (systemHealth.systemLoad.cpu > 60 ? 'bg-warning' : 'bg-success') %>"
                  style="width: <%= systemHealth.systemLoad.cpu %>%"></div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="progress-group">
              内存使用率
              <span class="float-right"><b>
                  <%= Math.round(systemHealth.systemLoad.memory) %>
                </b>/100%</span>
              <div class="progress progress-sm">
                <div
                  class="progress-bar <%= systemHealth.systemLoad.memory > 80 ? 'bg-danger' : (systemHealth.systemLoad.memory > 60 ? 'bg-warning' : 'bg-success') %>"
                  style="width: <%= systemHealth.systemLoad.memory %>%"></div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="progress-group">
              磁盘使用率
              <span class="float-right"><b>
                  <%= Math.round(systemHealth.systemLoad.disk) %>
                </b>/100%</span>
              <div class="progress progress-sm">
                <div
                  class="progress-bar <%= systemHealth.systemLoad.disk > 80 ? 'bg-danger' : (systemHealth.systemLoad.disk > 60 ? 'bg-warning' : 'bg-success') %>"
                  style="width: <%= systemHealth.systemLoad.disk %>%"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-md-6">
            <small class="text-muted">
              <i class="fas fa-clock"></i> 运行时间: <%= Math.floor(systemHealth.uptime / 3600) %>小时 <%=
                  Math.floor((systemHealth.uptime % 3600) / 60) %>分钟
            </small>
          </div>
          <div class="col-md-6">
            <small class="text-muted">
              <i class="fab fa-node-js"></i> Node.js: <%= systemHealth.nodeVersion %>
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户活跃度 -->
  <div class="col-lg-4">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-user-chart me-2"></i>
          用户活跃度
        </h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-12">
            <div class="progress-group">
              周活跃用户
              <span class="float-right"><b>
                  <%= stats.weeklyActiveUsers %>
                </b>/<%= stats.totalUsers %></span>
              <div class="progress progress-sm">
                <div class="progress-bar bg-primary"
                  style="width: <%= stats.totalUsers > 0 ? (stats.weeklyActiveUsers / stats.totalUsers * 100) : 0 %>%">
                </div>
              </div>
            </div>

            <div class="progress-group">
              月活跃用户
              <span class="float-right"><b>
                  <%= stats.monthlyActiveUsers %>
                </b>/<%= stats.totalUsers %></span>
              <div class="progress progress-sm">
                <div class="progress-bar bg-success"
                  style="width: <%= stats.totalUsers > 0 ? (stats.monthlyActiveUsers / stats.totalUsers * 100) : 0 %>%">
                </div>
              </div>
            </div>

            <div class="progress-group">
              活跃用户
              <span class="float-right"><b>
                  <%= stats.activeUsers %>
                </b>/<%= stats.totalUsers %></span>
              <div class="progress progress-sm">
                <div class="progress-bar bg-info"
                  style="width: <%= stats.totalUsers > 0 ? (stats.activeUsers / stats.totalUsers * 100) : 0 %>%"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-3">
          <small class="text-muted">
            <i class="fas fa-chart-line"></i> 平台知识库: <%= stats.publishedArticles + stats.helpArticles %>篇文章
          </small><br>
          <small class="text-muted">
            <i class="fas fa-bullhorn"></i> 活跃公告: <%= stats.activeAnnouncements %>条
          </small>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 最新租户和订单 -->
<div class="row">
  <!-- 最新租户 -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-building me-2"></i>
          最新租户
        </h3>
        <div class="card-tools">
          <a href="/tenants" class="btn btn-tool">
            <i class="fas fa-arrow-right"></i>
          </a>
        </div>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>租户名称</th>
                <th>联系人</th>
                <th>用户数</th>
                <th>鹅群数</th>
                <th>订阅计划</th>
              </tr>
            </thead>
            <tbody>
              <% if (recent_tenants && recent_tenants.length > 0) { %>
                <% recent_tenants.forEach(tenant => { %>
                  <tr>
                    <td>
                      <strong>
                        <%= tenant.tenant_name %>
                      </strong>
                      <br>
                      <small class="text-muted">
                        <%= tenant.tenant_code %>
                      </small>
                    </td>
                    <td>
                      <%= tenant.contact_person %>
                    </td>
                    <td><span class="badge bg-info">
                        <%= tenant.user_count %>
                      </span></td>
                    <td><span class="badge bg-warning">
                        <%= tenant.flock_count %>
                      </span></td>
                    <td>
                      <% if (tenant.subscription_plan==='basic' ) { %>
                        <span class="badge bg-secondary">基础版</span>
                        <% } else if (tenant.subscription_plan==='standard' ) { %>
                          <span class="badge bg-primary">标准版</span>
                          <% } else if (tenant.subscription_plan==='premium' ) { %>
                            <span class="badge bg-warning">高级版</span>
                            <% } else if (tenant.subscription_plan==='enterprise' ) { %>
                              <span class="badge bg-success">企业版</span>
                              <% } %>
                    </td>
                  </tr>
                  <% }) %>
                    <% } else { %>
                      <tr>
                        <td colspan="5" class="text-center text-muted py-3">暂无租户数据</td>
                      </tr>
                      <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- 最新订单 -->
  <div class="col-lg-6">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-shopping-cart me-2"></i>
          最新订单
        </h3>
        <div class="card-tools">
          <a href="/mall/orders" class="btn btn-tool">
            <i class="fas fa-arrow-right"></i>
          </a>
        </div>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>订单号</th>
                <th>租户</th>
                <th>金额</th>
                <th>状态</th>
                <th>时间</th>
              </tr>
            </thead>
            <tbody>
              <% if (recent_orders && recent_orders.length > 0) { %>
                <% recent_orders.forEach(order => { %>
                  <tr>
                    <td>
                      <small>
                        <%= order.order_no %>
                      </small>
                    </td>
                    <td>
                      <%= order.tenant_name || '未知租户' %>
                        <br>
                        <small class="text-muted">
                          <%= order.user_name || '' %>
                        </small>
                    </td>
                    <td><strong>¥<%= parseFloat(order.final_amount).toFixed(2) %></strong></td>
                    <td>
                      <% if (order.order_status==='completed' ) { %>
                        <span class="badge bg-success">已完成</span>
                        <% } else if (order.order_status==='shipped' ) { %>
                          <span class="badge bg-info">已发货</span>
                          <% } else if (order.order_status==='confirmed' ) { %>
                            <span class="badge bg-primary">已确认</span>
                            <% } else if (order.order_status==='pending' ) { %>
                              <span class="badge bg-warning">待处理</span>
                              <% } else if (order.order_status==='cancelled' ) { %>
                                <span class="badge bg-danger">已取消</span>
                                <% } %>
                    </td>
                    <td>
                      <small>
                        <%= new Date(order.created_at).toLocaleString('zh-CN', {month: 'short' , day: 'numeric' ,
                          hour: '2-digit' , minute: '2-digit' }) %>
                      </small>
                    </td>
                  </tr>
                  <% }) %>
                    <% } else { %>
                      <tr>
                        <td colspan="5" class="text-center text-muted py-3">暂无订单数据</td>
                      </tr>
                      <% } %>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 初始化仪表板
    initDashboard();

    // 每分钟刷新一次系统状态
    setInterval(refreshSystemStatus, 60000);

    // 绑定刷新按钮事件
    const refreshBtn = document.querySelector('.btn-tool');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', function (e) {
        e.preventDefault();
        refreshSystemStatus();
      });
    }
  });

  // 初始化仪表板
  function initDashboard() {
    // 添加数字动画效果
    animateNumbers();

    // 添加卡片进入动画
    const infoBoxes = document.querySelectorAll('.info-box');
    infoBoxes.forEach((box, index) => {
      box.style.opacity = '0';
      box.style.transform = 'translateY(20px)';
      setTimeout(() => {
        box.style.transition = 'all 0.5s ease';
        box.style.opacity = '1';
        box.style.transform = 'translateY(0)';
      }, index * 100);
    });
  }

  // 数字动画效果
  function animateNumbers() {
    const numbers = document.querySelectorAll('.info-box-number');
    numbers.forEach(numberElement => {
      const text = numberElement.textContent.trim();
      const numMatch = text.match(/\d+/);
      if (numMatch) {
        const target = parseInt(numMatch[0]);
        if (target > 0) {
          let current = 0;
          const increment = Math.max(1, Math.ceil(target / 30));
          const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
              current = target;
              clearInterval(timer);
            }
            numberElement.textContent = text.replace(/\d+/, current.toString());
          }, 50);
        }
      }
    });
  }

  // 刷新系统状态
  function refreshSystemStatus() {
    const refreshBtn = document.querySelector('.btn-tool');
    if (refreshBtn) {
      refreshBtn.classList.add('refreshing');
    }

    // 显示加载状态
    showLoadingState();

    // 模拟数据更新（实际项目中应该发送AJAX请求）
    setTimeout(() => {
      hideLoadingState();
      if (refreshBtn) {
        refreshBtn.classList.remove('refreshing');
      }
      showSuccessMessage('系统状态已更新');
    }, 1000);
  }

  // 显示加载状态
  function showLoadingState() {
    const systemMonitoring = document.querySelector('.system-monitoring');
    if (systemMonitoring) {
      systemMonitoring.style.opacity = '0.6';
      systemMonitoring.style.pointerEvents = 'none';
    }
  }

  // 隐藏加载状态
  function hideLoadingState() {
    const systemMonitoring = document.querySelector('.system-monitoring');
    if (systemMonitoring) {
      systemMonitoring.style.opacity = '1';
      systemMonitoring.style.pointerEvents = 'auto';
    }
  }

  // 显示成功消息
  function showSuccessMessage(message) {
    showToast(message, 'success');
  }

  // 显示提示消息
  function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 5000);
  }
</script>