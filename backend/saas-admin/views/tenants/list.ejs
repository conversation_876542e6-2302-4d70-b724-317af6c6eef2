<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-building"></i> 租户管理
                        </h3>
                        <div class="card-tools">
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 新增租户
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>租户代码</th>
                                        <th>租户名称</th>
                                        <th>联系人</th>
                                        <th>订阅计划</th>
                                        <th>状态</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (tenants && tenants.length > 0) { %>
                                        <% tenants.forEach(tenant => { %>
                                            <tr>
                                                <td><%= tenant.tenant_code %></td>
                                                <td><%= tenant.display_name || tenant.name %></td>
                                                <td><%= tenant.contact_name %></td>
                                                <td><%= tenant.subscription_plan %></td>
                                                <td>
                                                    <span class="badge bg-<%= tenant.status === 'active' ? 'success' : 'secondary' %>">
                                                        <%= tenant.status === 'active' ? '正常' : '暂停' %>
                                                    </span>
                                                </td>
                                                <td><%= new Date(tenant.created_at).toLocaleDateString() %></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary" onclick="manageTenant('<%= tenant.id %>')">
                                                        <i class="fas fa-cog"></i> 管理
                                                    </button>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">暂无租户数据</td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function manageTenant(tenantId) {
            window.location.href = '/tenant/' + tenantId + '/flocks';
        }
    </script>
</body>
</html>