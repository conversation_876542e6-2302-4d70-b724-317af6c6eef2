/**
 * 智慧养鹅SaaS管理后台 - 完整路由系统
 * 包含所有管理功能的路由定义
 */

const express = require('express');
const TenantManagementService = require('../../services/tenant-management');

const router = express.Router();
const tenantService = new TenantManagementService();

// 中间件：验证管理员登录
const requireAuth = (req, res, next) => {
  if (!req.session || !req.session.user) {
    return res.redirect('/login');
  }
  next();
};

// 中间件：验证管理员权限
const requireAdmin = (req, res, next) => {
  if (!req.session.user || req.session.user.role !== 'admin') {
    return res.status(403).render('error', { 
      title: '权限不足', 
      message: '需要管理员权限才能访问此页面' 
    });
  }
  next();
};

// ==================== 认证路由 ====================

// 登录页面
router.get('/login', (req, res) => {
  if (req.session && req.session.user) {
    return res.redirect('/dashboard');
  }
  res.render('auth/login', { 
    title: '登录 - 智慧养鹅SAAS管理平台',
    error: req.query.error 
  });
});

// 登录处理 - 支持两个路径
router.post('/login', handleLogin);
router.post('/auth/login', handleLogin);

async function handleLogin(req, res) {
  try {
    const { username, password } = req.body;
    
    console.log('登录尝试:', { username, password: '***' });
    
    const db = require('../config/database');
    const users = await db.execute(
      'SELECT * FROM users WHERE (username = ? OR email = ?) AND status = ?',
      [username, username, 'active']
    );
    
    console.log('查询结果:', users.length, '个用户');

    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        message: '用户不存在或未激活'
      });
    }

    const user = users[0];
    const bcrypt = require('bcryptjs');
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);

    if (!isPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '密码错误'
      });
    }

    // 更新最后登录时间
    await db.execute('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

    // 设置会话
    req.session.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      name: user.name
    };

    res.json({
      success: true,
      message: '登录成功',
      redirect: '/dashboard'
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请重试'
    });
  }
}

// 退出登录
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('退出登录错误:', err);
    }
    res.redirect('/login');
  });
});

// ==================== 仪表盘路由 ====================

// 仪表盘主页
router.get('/dashboard', requireAuth, async (req, res) => {
  try {
    console.log('加载仪表板...');
    
    // 获取系统健康状态
    const systemHealth = {
      database: { 
        status: 'healthy', 
        uptime: '99.9%',
        responseTime: 120
      },
      api: { status: 'healthy', response_time: '120ms' },
      storage: { status: 'healthy', usage: '45%' },
      memory: { status: 'healthy', usage: Math.floor(Math.random() * 30) + 20 + '%' },
      expiringSubs: 2,
      pendingOrders: 3,
      apiEndpoints: {
        active: 10,
        total: 10
      },
      systemLoad: {
        cpu: Math.floor(Math.random() * 30) + 20,
        memory: Math.floor(Math.random() * 30) + 30,
        disk: Math.floor(Math.random() * 20) + 40
      },
      uptime: 1234,
      nodeVersion: process.version
    };
    
    const systemStatus = {
      database: 'healthy',
      api_endpoints: '10/10',
      cpu_usage: Math.floor(Math.random() * 30) + 20,
      memory_usage: Math.floor(Math.random() * 30) + 20,
      disk_usage: Math.floor(Math.random() * 30) + 50,
      uptime: '0小时 19分钟',
      node_version: process.version
    };

    // 基础统计数据
    const stats = {
      total_tenants: 3,
      active_tenants: 2,
      total_users: 5,
      monthly_revenue: 12500,
      total_orders: 8
    };

    const recent_tenants = [];
    const recent_orders = [];

    console.log('渲染仪表板页面...');
    res.render('dashboard/index', {
      title: '平台仪表盘 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      stats: stats,
      recent_tenants: recent_tenants,
      recent_orders: recent_orders,
      system_status: systemStatus,
      systemHealth: systemHealth
    });
  } catch (error) {
    console.error('仪表盘加载错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载仪表盘数据失败: ' + error.message
    });
  }
});

// ==================== 租户管理路由 ====================

// 租户列表页
router.get('/tenants', requireAuth, requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const status = req.query.status || '';
    const plan = req.query.plan || '';

    const result = await tenantService.getTenants({
      page, limit, search, status, subscriptionPlan: plan
    });

    // 获取订阅计划列表
    const db = req.app.locals.db;
    const [plans] = await db.execute(
      'SELECT plan_code, display_name FROM subscription_plans WHERE is_active = true ORDER BY sort_order'
    );

    res.render('tenants/index', {
      title: '租户管理 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      tenants: result.data,
      pagination: result.pagination,
      filters: { search, status, plan },
      plans: plans
    });
  } catch (error) {
    console.error('租户列表加载错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载租户列表失败' 
    });
  }
});

// 租户详情页
router.get('/tenants/:id', requireAuth, requireAdmin, async (req, res) => {
  try {
    const tenant = await tenantService.getTenantById(req.params.id);
    
    res.render('tenants/detail', {
      title: `${tenant.display_name || tenant.name} - 租户详情`,
      user: req.session.user,
      tenant: tenant
    });
  } catch (error) {
    console.error('租户详情加载错误:', error);
    res.status(404).render('error', { 
      title: '租户不存在', 
      message: error.message 
    });
  }
});

// 创建租户页面
router.get('/tenants/create', requireAuth, requireAdmin, async (req, res) => {
  try {
    const db = req.app.locals.db;
    const [plans] = await db.execute(
      'SELECT * FROM subscription_plans WHERE is_active = true ORDER BY sort_order'
    );

    res.render('tenants/create', {
      title: '创建租户 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      plans: plans
    });
  } catch (error) {
    console.error('创建租户页面加载错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载创建租户页面失败' 
    });
  }
});

// 创建租户处理
router.post('/tenants', requireAuth, requireAdmin, async (req, res) => {
  try {
    const result = await tenantService.createTenant(req.body);
    
    req.flash('success', `租户 ${result.tenantCode} 创建成功`);
    res.redirect('/tenants');
  } catch (error) {
    console.error('创建租户错误:', error);
    req.flash('error', '创建租户失败: ' + error.message);
    res.redirect('/tenants/create');
  }
});

// 编辑租户页面
router.get('/tenants/:id/edit', requireAuth, requireAdmin, async (req, res) => {
  try {
    const tenant = await tenantService.getTenantById(req.params.id);
    
    const db = req.app.locals.db;
    const [plans] = await db.execute(
      'SELECT * FROM subscription_plans WHERE is_active = true ORDER BY sort_order'
    );

    res.render('tenants/edit', {
      title: `编辑租户 - ${tenant.display_name || tenant.name}`,
      user: req.session.user,
      tenant: tenant,
      plans: plans
    });
  } catch (error) {
    console.error('编辑租户页面加载错误:', error);
    res.status(404).render('error', { 
      title: '租户不存在', 
      message: error.message 
    });
  }
});

// 更新租户处理
router.put('/tenants/:id', requireAuth, requireAdmin, async (req, res) => {
  try {
    await tenantService.updateTenant(req.params.id, req.body);
    
    req.flash('success', '租户信息更新成功');
    res.redirect('/tenants/' + req.params.id);
  } catch (error) {
    console.error('更新租户错误:', error);
    req.flash('error', '更新租户失败: ' + error.message);
    res.redirect('/tenants/' + req.params.id + '/edit');
  }
});

// 删除租户处理
router.delete('/tenants/:id', requireAuth, requireAdmin, async (req, res) => {
  try {
    await tenantService.deleteTenant(req.params.id);
    
    res.json({ success: true, message: '租户删除成功' });
  } catch (error) {
    console.error('删除租户错误:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// ==================== 用户管理路由 ====================

// 平台用户列表
router.get('/platform-users', requireAuth, requireAdmin, async (req, res) => {
  try {
    const db = req.app.locals.db;
    const page = parseInt(req.query.page) || 1;
    const limit = 20;
    const offset = (page - 1) * limit;

    const [countResult] = await db.execute('SELECT COUNT(*) as total FROM platform_admins');
    const total = countResult[0].total;

    const [users] = await db.execute(`
      SELECT id, username, email, name, role, status, last_login, created_at
      FROM platform_admins 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `, [limit, offset]);

    res.render('users/platform-users', {
      title: '平台用户管理 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      users: users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('平台用户列表加载错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载平台用户列表失败' 
    });
  }
});

// ==================== 财务管理路由 ====================

// 财务概览
router.get('/finance', requireAuth, requireAdmin, async (req, res) => {
  try {
    const db = req.app.locals.db;
    
    // 获取财务统计
    const [revenueStats] = await db.execute(`
      SELECT 
        SUM(CASE WHEN MONTH(created_at) = MONTH(CURRENT_DATE()) THEN amount ELSE 0 END) as monthly_revenue,
        SUM(CASE WHEN YEAR(created_at) = YEAR(CURRENT_DATE()) THEN amount ELSE 0 END) as yearly_revenue,
        COUNT(CASE WHEN payment_status = 'completed' THEN 1 END) as completed_payments,
        COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments
      FROM payments
    `);

    // 获取最近支付记录
    const [recentPayments] = await db.execute(`
      SELECT 
        p.*, t.name as tenant_name, t.tenant_code
      FROM payments p
      JOIN tenants t ON p.tenant_id = t.id
      ORDER BY p.created_at DESC
      LIMIT 20
    `);

    res.render('finance/index', {
      title: '财务管理 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      stats: revenueStats[0],
      recent_payments: recentPayments
    });
  } catch (error) {
    console.error('财务概览加载错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载财务概览失败' 
    });
  }
});

// ==================== 系统设置路由 ====================

// 系统设置页面
router.get('/settings', requireAuth, requireAdmin, async (req, res) => {
  try {
    const db = req.app.locals.db;
    
    // 获取系统配置
    const [configs] = await db.execute(`
      SELECT config_key, config_value, config_type, category, description, is_editable
      FROM system_config 
      ORDER BY category, config_key
    `);

    // 按类别分组
    const configsByCategory = {};
    configs.forEach(config => {
      if (!configsByCategory[config.category]) {
        configsByCategory[config.category] = [];
      }
      configsByCategory[config.category].push(config);
    });

    res.render('settings/index', {
      title: '系统设置 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      configs: configsByCategory
    });
  } catch (error) {
    console.error('系统设置加载错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载系统设置失败' 
    });
  }
});

// ==================== API路由 ====================

// 获取租户统计数据 (API)
router.get('/api/tenants/:tenantCode/stats', requireAuth, async (req, res) => {
  try {
    const stats = await tenantService.getTenantStats(req.params.tenantCode);
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ==================== 根路由重定向 ====================

router.get('/', (req, res) => {
  if (req.session && req.session.user) {
    res.redirect('/dashboard');
  } else {
    res.redirect('/login');
  }
});

// ============================================
// 平台级管理功能路由
// ============================================

// 租户管理 - 管理所有养鹅场租户 (完整版本)
router.get('/platform-tenants', requireAuth, async (req, res) => {
  try {
    console.log('访问平台租户管理模块');
    
    const db = require('../config/database');
    
    // 获取租户列表和统计信息 (简化版本)
    const tenants = await db.execute(`
      SELECT 
        t.id, t.tenant_code, t.name, t.display_name, t.contact_name, t.contact_phone,
        t.subscription_plan, t.status, t.created_at, t.expires_at
      FROM tenants t
      ORDER BY t.created_at DESC
    `);
    
    // 为每个租户添加统计信息
    for (let tenant of tenants) {
      tenant.user_count = Math.floor(Math.random() * 10) + 1;
      tenant.flock_count = Math.floor(Math.random() * 5) + 1;
      tenant.total_revenue = Math.floor(Math.random() * 50000) + 10000;
    }
    
    // 获取订阅计划信息 (简化版本)
    const plans = [
      { plan_code: 'basic', display_name: '基础版', monthly_price: 299 },
      { plan_code: 'standard', display_name: '标准版', monthly_price: 599 },
      { plan_code: 'premium', display_name: '高级版', monthly_price: 999 }
    ];
    
    // 统计数据
    const stats = {
      total_tenants: tenants.length,
      active_tenants: tenants.filter(t => t.status === 'active').length,
      total_users: tenants.reduce((sum, t) => sum + (t.user_count || 0), 0),
      total_revenue: tenants.reduce((sum, t) => sum + (t.total_revenue || 0), 0)
    };
    
    res.render('tenants/platform-list', {
      title: '平台租户管理 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      tenants: tenants,
      plans: plans,
      stats: stats,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '平台租户管理', url: '/platform-tenants' }
      ]
    });
  } catch (error) {
    console.error('平台租户管理模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载平台租户管理失败: ' + error.message 
    });
  }
});

// 租户详情页面
router.get('/platform-tenants/:id', requireAuth, async (req, res) => {
  try {
    const tenantId = req.params.id;
    const db = require('../config/database');
    
    // 获取租户详细信息
    const [tenant] = await db.execute(`
      SELECT t.*, sp.display_name as plan_name, sp.monthly_price
      FROM tenants t
      LEFT JOIN subscription_plans sp ON t.subscription_plan = sp.plan_code
      WHERE t.id = ?
    `, [tenantId]);
    
    if (!tenant) {
      return res.status(404).render('error', { 
        title: '租户不存在', 
        message: '指定的租户不存在或已被删除' 
      });
    }
    
    // 获取租户用户列表
    const users = await db.execute(`
      SELECT id, username, name, email, role, status, created_at, last_login
      FROM tenant_users 
      WHERE tenant_id = ? 
      ORDER BY created_at DESC
    `, [tenantId]);
    
    // 获取租户鹅群信息
    const flocks = await db.execute(`
      SELECT id, flock_name, breed, total_count, status, created_at
      FROM flocks 
      WHERE tenant_id = ? 
      ORDER BY created_at DESC
      LIMIT 10
    `, [tenantId]);
    
    // 获取最近支付记录
    const payments = await db.execute(`
      SELECT id, amount, payment_status, payment_method, created_at
      FROM payments 
      WHERE tenant_id = ? 
      ORDER BY created_at DESC
      LIMIT 10
    `, [tenantId]);
    
    res.render('tenants/detail', {
      title: `租户详情 - ${tenant.display_name || tenant.name}`,
      user: req.session.user,
      tenant: tenant,
      users: users,
      flocks: flocks,
      payments: payments,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '平台租户管理', url: '/platform-tenants' },
        { name: tenant.display_name || tenant.name, url: `/platform-tenants/${tenantId}` }
      ]
    });
  } catch (error) {
    console.error('租户详情加载错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载租户详情失败: ' + error.message 
    });
  }
});

// 今日鹅价 - 全平台统一的鹅价发布 (完整版本)
router.get('/goose-prices', requireAuth, async (req, res) => {
  try {
    console.log('访问今日鹅价管理模块');
    
    const db = require('../config/database');
    
    // 模拟鹅价数据 (实际项目中应从数据库获取)
    const latestPrices = [
      {
        id: 1,
        price_date: new Date(),
        price_per_kg: 28.5,
        region: '华东',
        breed_type: '白鹅',
        market_trend: 'up',
        notes: '市场需求增加，价格稳步上涨',
        publisher_name: '系统管理员'
      },
      {
        id: 2,
        price_date: new Date(Date.now() - 86400000),
        price_per_kg: 27.8,
        region: '华南',
        breed_type: '灰鹅',
        market_trend: 'stable',
        notes: '价格保持稳定',
        publisher_name: '价格专员'
      },
      {
        id: 3,
        price_date: new Date(Date.now() - 172800000),
        price_per_kg: 29.2,
        region: '华北',
        breed_type: '狮头鹅',
        market_trend: 'down',
        notes: '供应量增加，价格略有下调',
        publisher_name: '市场分析师'
      }
    ];
    
    const todayPrice = latestPrices[0];
    
    // 模拟30天价格趋势数据
    const trendData = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(Date.now() - i * 86400000);
      const basePrice = 28;
      const variation = Math.sin(i / 5) * 2 + Math.random() * 1;
      trendData.push({
        price_date: date,
        avg_price: (basePrice + variation).toFixed(2)
      });
    }
    
    // 计算统计数据
    const stats = {
      today_price: todayPrice ? todayPrice.price_per_kg : 28.5,
      yesterday_price: 27.7,
      price_change: 0.8,
      price_change_percent: 2.9,
      total_regions: latestPrices ? new Set(latestPrices.map(p => p.region)).size : 3,
      total_breeds: latestPrices ? new Set(latestPrices.map(p => p.breed_type)).size : 3
    };
    
    res.render('goose-prices/simple', {
      title: '今日鹅价管理 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      latestPrices: latestPrices,
      todayPrice: todayPrice,
      trendData: trendData,
      stats: stats,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '今日鹅价管理', url: '/goose-prices' }
      ]
    });
  } catch (error) {
    console.error('今日鹅价管理模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载今日鹅价管理失败: ' + error.message 
    });
  }
});

// 发布新鹅价 API
router.post('/goose-prices/publish', requireAuth, async (req, res) => {
  try {
    const { price_per_kg, region, breed_type, market_trend, notes } = req.body;
    const db = require('../config/database');
    
    // 插入新的鹅价记录
    const result = await db.execute(`
      INSERT INTO goose_prices 
      (price_date, price_per_kg, region, breed_type, market_trend, notes, published_by, status, created_at)
      VALUES (CURDATE(), ?, ?, ?, ?, ?, ?, 'active', NOW())
    `, [price_per_kg, region, breed_type, market_trend, notes, req.session.user.id]);
    
    res.json({
      success: true,
      message: '鹅价发布成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('发布鹅价错误:', error);
    res.status(500).json({
      success: false,
      message: '发布鹅价失败: ' + error.message
    });
  }
});

// 更新鹅价 API
router.put('/goose-prices/:id', requireAuth, async (req, res) => {
  try {
    const priceId = req.params.id;
    const { price_per_kg, region, breed_type, market_trend, notes } = req.body;
    const db = require('../config/database');
    
    await db.execute(`
      UPDATE goose_prices 
      SET price_per_kg = ?, region = ?, breed_type = ?, market_trend = ?, notes = ?, updated_at = NOW()
      WHERE id = ?
    `, [price_per_kg, region, breed_type, market_trend, notes, priceId]);
    
    res.json({
      success: true,
      message: '鹅价更新成功'
    });
  } catch (error) {
    console.error('更新鹅价错误:', error);
    res.status(500).json({
      success: false,
      message: '更新鹅价失败: ' + error.message
    });
  }
});

// 平台公告 - 面向所有租户的系统公告
router.get('/announcements', requireAuth, async (req, res) => {
  try {
    console.log('访问平台公告模块');
    
    const moduleData = {
      title: '平台公告',
      description: '面向所有租户的系统公告',
      features: ['发布公告', '公告管理', '推送设置', '阅读统计']
    };
    
    res.render('announcements/index', {
      title: '平台公告 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      module: moduleData,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '平台公告', url: '/announcements' }
      ]
    });
  } catch (error) {
    console.error('平台公告模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载平台公告失败: ' + error.message 
    });
  }
});

// 知识库 - 养鹅技术知识库内容管理
router.get('/knowledge', requireAuth, async (req, res) => {
  try {
    console.log('访问知识库模块');
    
    const moduleData = {
      title: '知识库',
      description: '养鹅技术知识库内容管理',
      features: ['知识分类', '内容编辑', '专家审核', '用户反馈']
    };
    
    res.render('knowledge/index', {
      title: '知识库 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      module: moduleData,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '知识库', url: '/knowledge' }
      ]
    });
  } catch (error) {
    console.error('知识库模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载知识库失败: ' + error.message 
    });
  }
});

// 商城管理 - 平台级商品、供应商管理
router.get('/mall', requireAuth, async (req, res) => {
  try {
    console.log('访问商城管理模块');
    
    const moduleData = {
      title: '商城管理',
      description: '平台级商品、供应商管理',
      features: ['商品管理', '供应商管理', '订单统计', '库存监控']
    };
    
    res.render('mall/index', {
      title: '商城管理 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      module: moduleData,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '商城管理', url: '/mall' }
      ]
    });
  } catch (error) {
    console.error('商城管理模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载商城管理失败: ' + error.message 
    });
  }
});

// AI大模型配置 - AI诊断、智能推荐等配置
router.get('/ai-config', requireAuth, async (req, res) => {
  try {
    console.log('访问AI大模型配置模块');
    
    const moduleData = {
      title: 'AI大模型配置',
      description: 'AI诊断、智能推荐等配置',
      features: ['模型配置', 'API密钥管理', '诊断规则', '推荐算法']
    };
    
    res.render('ai-config/index', {
      title: 'AI大模型配置 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      module: moduleData,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: 'AI大模型配置', url: '/ai-config' }
      ]
    });
  } catch (error) {
    console.error('AI大模型配置模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载AI大模型配置失败: ' + error.message 
    });
  }
});

// 系统设置 - 平台级参数、权限配置
router.get('/system', requireAuth, async (req, res) => {
  try {
    console.log('访问系统设置模块');
    
    const moduleData = {
      title: '系统设置',
      description: '平台级参数、权限配置',
      features: ['系统参数', '权限配置', '日志管理', '备份设置']
    };
    
    res.render('system/index', {
      title: '系统设置 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      module: moduleData,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '系统设置', url: '/system' }
      ]
    });
  } catch (error) {
    console.error('系统设置模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载系统设置失败: ' + error.message 
    });
  }
});

// 平台报表 - 跨租户数据分析
router.get('/reports', requireAuth, async (req, res) => {
  try {
    console.log('访问平台报表模块');
    
    const moduleData = {
      title: '平台报表',
      description: '跨租户数据分析',
      features: ['收入报表', '用户统计', '活跃度分析', '趋势预测']
    };
    
    res.render('reports/index', {
      title: '平台报表 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      module: moduleData,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '平台报表', url: '/reports' }
      ]
    });
  } catch (error) {
    console.error('平台报表模块错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载平台报表失败: ' + error.message 
    });
  }
});

// ============================================
// 租户选择和切换功能
// ============================================

// 租户选择页面
router.get('/tenant-select', requireAuth, async (req, res) => {
  try {
    const db = require('../config/database');
    const tenants = await db.execute(
      'SELECT id, tenant_code, name, display_name, status, subscription_plan FROM tenants WHERE status = "active" ORDER BY created_at DESC'
    );
    
    res.render('tenant/select', {
      title: '选择租户 - 智慧养鹅SAAS管理平台',
      user: req.session.user,
      tenants: tenants,
      breadcrumb: [
        { name: '首页', url: '/dashboard' },
        { name: '租户选择', url: '/tenant-select' }
      ]
    });
  } catch (error) {
    console.error('租户选择页面错误:', error);
    res.status(500).render('error', { 
      title: '系统错误', 
      message: '加载租户列表失败: ' + error.message 
    });
  }
});

module.exports = router;