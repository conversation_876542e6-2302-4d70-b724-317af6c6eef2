# Page snapshot

```yaml
- generic [ref=e6]:
  - generic [ref=e7]: 
  - heading "Server Error" [level=2] [ref=e8]
  - paragraph [ref=e9]: "/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/main.ejs:160 158| <a class=\"nav-link dropdown-toggle\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\"> 159| <i class=\"fas fa-user-circle\"></i> >> 160| <%= user ? user.username : 'admin' %> 161| </a> 162| <ul class=\"dropdown-menu\"> 163| <li><a class=\"dropdown-item\" href=\"/profile\">个人资料</a></li> user is not defined"
  - generic [ref=e11]: "ReferenceError: /Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/main.ejs:160 158| <a class=\"nav-link dropdown-toggle\" href=\"#\" role=\"button\" data-bs-toggle=\"dropdown\"> 159| <i class=\"fas fa-user-circle\"></i> >> 160| <%= user ? user.username : 'admin' %> 161| </a> 162| <ul class=\"dropdown-menu\"> 163| <li><a class=\"dropdown-item\" href=\"/profile\">个人资料</a></li> user is not defined at eval (\"/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/views/layouts/main.ejs\":18:26) at main (/Volumes/DATA/千问/智慧养鹅全栈/node_modules/ejs/lib/ejs.js:703:17) at tryHandleCache (/Volumes/DATA/千问/智慧养鹅全栈/node_modules/ejs/lib/ejs.js:274:36) at exports.renderFile [as engine] (/Volumes/DATA/千问/智慧养鹅全栈/node_modules/ejs/lib/ejs.js:491:10) at View.render (/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/node_modules/express/lib/view.js:135:8) at tryRender (/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/node_modules/express/lib/application.js:657:10) at Function.render (/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/node_modules/express/lib/application.js:609:3) at ServerResponse.render (/Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/node_modules/express/lib/response.js:1049:7) at /Volumes/DATA/千问/智慧养鹅全栈/backend/saas-admin/node_modules/express-ejs-layouts/lib/express-layouts.js:113:20 at tryHandleCache (/Volumes/DATA/千问/智慧养鹅全栈/node_modules/ejs/lib/ejs.js:280:5)"
  - generic [ref=e12]:
    - link " 返回首页" [ref=e13] [cursor=pointer]:
      - /url: /dashboard
      - generic [ref=e14] [cursor=pointer]: 
      - text: 返回首页
    - link " 返回上页" [ref=e15] [cursor=pointer]:
      - /url: javascript:history.back()
      - generic [ref=e16] [cursor=pointer]: 
      - text: 返回上页
```