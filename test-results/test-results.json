{"config": {"configFile": "/Volumes/DATA/千问/智慧养鹅全栈/playwright.config.js", "rootDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-setup.js", "globalTeardown": "/Volumes/DATA/千问/智慧养鹅全栈/tests/global-teardown.js", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/test-results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Volumes/DATA/千问/智慧养鹅全栈/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Volumes/DATA/千问/智慧养鹅全栈/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.55.0", "workers": 4, "webServer": null}, "suites": [{"title": "saas-admin-complete.spec.js", "file": "saas-admin-complete.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "SaaS Admin Interface Complete Testing", "file": "saas-admin-complete.spec.js", "line": 3, "column": 6, "specs": [{"title": "Should load dashboard successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 13906, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:16:18", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 18, "line": 16}, "snippet": "  14 |     if (currentUrl.includes('/login')) {\n  15 |       // If redirected to login, test login functionality\n> 16 |       await page.fill('input[name=\"username\"]', 'super_admin');\n     |                  ^\n  17 |       await page.fill('input[name=\"password\"]', 'admin123');\n  18 |       await page.click('button[type=\"submit\"]');\n  19 |       "}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 18, "line": 16}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n  14 |     if (currentUrl.includes('/login')) {\n  15 |       // If redirected to login, test login functionality\n> 16 |       await page.fill('input[name=\"username\"]', 'super_admin');\n     |                  ^\n  17 |       await page.fill('input[name=\"password\"]', 'admin123');\n  18 |       await page.click('button[type=\"submit\"]');\n  19 |       \n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:16:18"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:57:31.558Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-baece-load-dashboard-successfully-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-baece-load-dashboard-successfully-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-baece-load-dashboard-successfully-chromium/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 18, "line": 16}}], "status": "unexpected"}], "id": "f9415b32e8ea7438ec88-e7d7e8a92f09afdb985a", "file": "saas-admin-complete.spec.js", "line": 5, "column": 7}, {"title": "Should navigate to tenant management", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 16619, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nLocator: locator('h2')\nExpected string: \u001b[32m\"租户管理\"\u001b[39m\nReceived string: \u001b[31m\"Server Error\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to <h2 class=\"mb-3\">Server Error</h2>\u001b[22m\n\u001b[2m       - unexpected value \"Server Error\"\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nLocator: locator('h2')\nExpected string: \u001b[32m\"租户管理\"\u001b[39m\nReceived string: \u001b[31m\"Server Error\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to <h2 class=\"mb-3\">Server Error</h2>\u001b[22m\n\u001b[2m       - unexpected value \"Server Error\"\u001b[22m\n\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:37:38", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 38, "line": 37}, "snippet": "  35 |     \n  36 |     // Should see tenant management page\n> 37 |     await expect(page.locator('h2')).toContainText('租户管理');\n     |                                      ^\n  38 |     \n  39 |     // Check for tenant list table or empty state\n  40 |     const hasTable = await page.locator('table').isVisible();"}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 38, "line": 37}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nLocator: locator('h2')\nExpected string: \u001b[32m\"租户管理\"\u001b[39m\nReceived string: \u001b[31m\"Server Error\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to <h2 class=\"mb-3\">Server Error</h2>\u001b[22m\n\u001b[2m       - unexpected value \"Server Error\"\u001b[22m\n\n\n  35 |     \n  36 |     // Should see tenant management page\n> 37 |     await expect(page.locator('h2')).toContainText('租户管理');\n     |                                      ^\n  38 |     \n  39 |     // Check for tenant list table or empty state\n  40 |     const hasTable = await page.locator('table').isVisible();\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:37:38"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:57:50.062Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-ee665-vigate-to-tenant-management-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-ee665-vigate-to-tenant-management-chromium/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-ee665-vigate-to-tenant-management-chromium/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 38, "line": 37}}], "status": "unexpected"}], "id": "f9415b32e8ea7438ec88-dcc23671b8e2094c51dd", "file": "saas-admin-complete.spec.js", "line": 31, "column": 7}, {"title": "Should handle new tenant creation form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 1716, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:08.448Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-35cfa75df9846ceff0cd", "file": "saas-admin-complete.spec.js", "line": 46, "column": 7}, {"title": "Should load platform users page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 2309, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:10.763Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-371d9b7b7b99d12703e1", "file": "saas-admin-complete.spec.js", "line": 70, "column": 7}, {"title": "Should load finance page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 2872, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:13.090Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-d9e739a9e6f450246c3a", "file": "saas-admin-complete.spec.js", "line": 80, "column": 7}, {"title": "Should load settings page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 2134, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:15.972Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-91542ee12009c00b16c9", "file": "saas-admin-complete.spec.js", "line": 90, "column": 7}, {"title": "Should load monitoring page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 2152, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:18.114Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-17d311119751784ae775", "file": "saas-admin-complete.spec.js", "line": 100, "column": 7}, {"title": "Should test navigation between pages", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 2126, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:20.277Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-1525c7fc19073a3c5acd", "file": "saas-admin-complete.spec.js", "line": 110, "column": 7}, {"title": "Should test responsive design elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "passed", "duration": 2286, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:22.411Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-24c6a499da973c6804ad", "file": "saas-admin-complete.spec.js", "line": 136, "column": 7}, {"title": "Should load dashboard successfully", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 13633, "error": {"message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n", "stack": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:16:18", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 18, "line": 16}, "snippet": "  14 |     if (currentUrl.includes('/login')) {\n  15 |       // If redirected to login, test login functionality\n> 16 |       await page.fill('input[name=\"username\"]', 'super_admin');\n     |                  ^\n  17 |       await page.fill('input[name=\"password\"]', 'admin123');\n  18 |       await page.click('button[type=\"submit\"]');\n  19 |       "}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 18, "line": 16}, "message": "TimeoutError: page.fill: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n  14 |     if (currentUrl.includes('/login')) {\n  15 |       // If redirected to login, test login functionality\n> 16 |       await page.fill('input[name=\"username\"]', 'super_admin');\n     |                  ^\n  17 |       await page.fill('input[name=\"password\"]', 'admin123');\n  18 |       await page.click('button[type=\"submit\"]');\n  19 |       \n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:16:18"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:57:31.556Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-baece-load-dashboard-successfully-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-baece-load-dashboard-successfully-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-baece-load-dashboard-successfully-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 18, "line": 16}}], "status": "unexpected"}], "id": "f9415b32e8ea7438ec88-986858a49589c809bd57", "file": "saas-admin-complete.spec.js", "line": 5, "column": 7}, {"title": "Should navigate to tenant management", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 2, "parallelIndex": 1, "status": "failed", "duration": 13102, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nLocator: locator('h2')\nExpected string: \u001b[32m\"租户管理\"\u001b[39m\nReceived string: \u001b[31m\"Server Error\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to <h2 class=\"mb-3\">Server Error</h2>\u001b[22m\n\u001b[2m       - unexpected value \"Server Error\"\u001b[22m\n", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nLocator: locator('h2')\nExpected string: \u001b[32m\"租户管理\"\u001b[39m\nReceived string: \u001b[31m\"Server Error\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to <h2 class=\"mb-3\">Server Error</h2>\u001b[22m\n\u001b[2m       - unexpected value \"Server Error\"\u001b[22m\n\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:37:38", "location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 38, "line": 37}, "snippet": "  35 |     \n  36 |     // Should see tenant management page\n> 37 |     await expect(page.locator('h2')).toContainText('租户管理');\n     |                                      ^\n  38 |     \n  39 |     // Check for tenant list table or empty state\n  40 |     const hasTable = await page.locator('table').isVisible();"}, "errors": [{"location": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 38, "line": 37}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m failed\n\nLocator: locator('h2')\nExpected string: \u001b[32m\"租户管理\"\u001b[39m\nReceived string: \u001b[31m\"Server Error\"\u001b[39m\nTimeout: 10000ms\n\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('h2')\u001b[22m\n\u001b[2m    14 × locator resolved to <h2 class=\"mb-3\">Server Error</h2>\u001b[22m\n\u001b[2m       - unexpected value \"Server Error\"\u001b[22m\n\n\n  35 |     \n  36 |     // Should see tenant management page\n> 37 |     await expect(page.locator('h2')).toContainText('租户管理');\n     |                                      ^\n  38 |     \n  39 |     // Check for tenant list table or empty state\n  40 |     const hasTable = await page.locator('table').isVisible();\n    at /Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js:37:38"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:57:50.063Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-ee665-vigate-to-tenant-management-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-ee665-vigate-to-tenant-management-Mobile-Chrome/video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "/Volumes/DATA/千问/智慧养鹅全栈/test-results/saas-admin-complete-SaaS-A-ee665-vigate-to-tenant-management-Mobile-Chrome/error-context.md"}], "errorLocation": {"file": "/Volumes/DATA/千问/智慧养鹅全栈/tests/saas-admin-complete.spec.js", "column": 38, "line": 37}}], "status": "unexpected"}], "id": "f9415b32e8ea7438ec88-a2c9a882c5471c1667e5", "file": "saas-admin-complete.spec.js", "line": 31, "column": 7}, {"title": "Should handle new tenant creation form", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 2581, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:04.579Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-38c896b426731c21c8de", "file": "saas-admin-complete.spec.js", "line": 46, "column": 7}, {"title": "Should load platform users page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 2116, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:07.665Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-b95c0100e7a66d63ec8c", "file": "saas-admin-complete.spec.js", "line": 70, "column": 7}, {"title": "Should load finance page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 1996, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:09.793Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-4bcf6316082ade9b3378", "file": "saas-admin-complete.spec.js", "line": 80, "column": 7}, {"title": "Should load settings page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 2408, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:11.799Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-ab8a11a0d81fcb04a791", "file": "saas-admin-complete.spec.js", "line": 90, "column": 7}, {"title": "Should load monitoring page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 2327, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:14.215Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-90db6e6d89bbecb21bda", "file": "saas-admin-complete.spec.js", "line": 100, "column": 7}, {"title": "Should test navigation between pages", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 2283, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:16.552Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-09a3eb53c089e131c7d4", "file": "saas-admin-complete.spec.js", "line": 110, "column": 7}, {"title": "Should test responsive design elements", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 2246, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-27T13:58:18.843Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "f9415b32e8ea7438ec88-ca7fa5d2c62e9da2a593", "file": "saas-admin-complete.spec.js", "line": 136, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-27T13:57:30.760Z", "duration": 54091.224, "expected": 14, "skipped": 0, "unexpected": 4, "flaky": 0}}