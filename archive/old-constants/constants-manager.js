/**
 * 常量管理器
 * 提供统一的常量访问和管理接口
 */
const API = require('./api.constants.js');
const IMAGES = require('./images.constants.js');
const UI = require('./ui.constants.js');
const BUSINESS = require('./business.constants.js');
const CONFIG = require('./config.constants.js');

/**
 * 常量管理器类
 * 提供常量的获取、验证、搜索等功能
 */
class ConstantsManager {
  constructor() {
    this.constants = {
      API,
      IMAGES,
      UI,
      BUSINESS,
      CONFIG
    };
    
    // 缓存常用的常量路径
    this.cache = new Map();
    this.searchIndex = this.buildSearchIndex();
  }

  /**
   * 获取常量值
   * @param {string} path 常量路径 (如: 'BUSINESS.USER.ROLES.ADMIN')
   * @returns {*} 常量值
   */
  get(path) {
    if (this.cache.has(path)) {
      return this.cache.get(path);
    }

    const parts = path.split('.');
    let current = this.constants;

    for (const part of parts) {
      if (current[part] === undefined) {
        return undefined;
      }
      current = current[part];
    }

    // 缓存结果
    this.cache.set(path, current);
    return current;
  }

  /**
   * 检查常量是否存在
   * @param {string} path 常量路径
   * @returns {boolean} 是否存在
   */
  has(path) {
    return this.get(path) !== undefined;
  }

  /**
   * 获取业务常量
   * @param {string} module 模块名 (如: 'USER', 'FLOCK')
   * @param {string} key 键名 (如: 'ROLES', 'STATUS')
   * @returns {*} 常量值
   */
  getBusiness(module, key) {
    return this.get(`BUSINESS.${module}.${key}`);
  }

  /**
   * 获取配置常量
   * @param {string} module 模块名
   * @param {string} key 键名
   * @returns {*} 配置值
   */
  getConfig(module, key) {
    return this.get(`CONFIG.${module}.${key}`);
  }

  /**
   * 获取UI常量
   * @param {string} key 键名
   * @returns {*} UI常量值
   */
  getUI(key) {
    return this.get(`UI.${key}`);
  }

  /**
   * 获取图片常量
   * @param {string} category 分类 (如: 'ICONS', 'DEFAULTS')
   * @param {string} key 键名
   * @returns {string} 图片路径
   */
  getImage(category, key) {
    return this.get(`IMAGES.${category}.${key}`);
  }

  /**
   * 获取API常量
   * @param {string} key 键名
   * @returns {*} API常量值
   */
  getAPI(key) {
    return this.get(`API.${key}`);
  }

  /**
   * 获取验证规则
   * @param {string} type 验证类型
   * @returns {RegExp|Object} 验证规则
   */
  getValidation(type) {
    return this.get(`BUSINESS.VALIDATION.${type}`);
  }

  /**
   * 获取枚举显示名称
   * @param {string} enumPath 枚举路径
   * @param {string} value 枚举值
   * @returns {string} 显示名称
   */
  getEnumName(enumPath, value) {
    const namePath = enumPath.replace(/\.([A-Z_]+)$/, '.${1}_NAMES');
    const names = this.get(namePath);
    return names ? names[value] : value;
  }

  /**
   * 搜索常量
   * @param {string} keyword 关键词
   * @returns {Array} 搜索结果
   */
  search(keyword) {
    const results = [];
    const lowerKeyword = keyword.toLowerCase();

    for (const [path, value] of this.searchIndex.entries()) {
      if (path.toLowerCase().includes(lowerKeyword) || 
          (typeof value === 'string' && value.toLowerCase().includes(lowerKeyword))) {
        results.push({
          path,
          value,
          type: typeof value
        });
      }
    }

    return results.slice(0, 50); // 限制结果数量
  }

  /**
   * 获取常量路径的所有键
   * @param {string} path 常量路径
   * @returns {Array} 键列表
   */
  getKeys(path) {
    const obj = this.get(path);
    return obj && typeof obj === 'object' ? Object.keys(obj) : [];
  }

  /**
   * 获取常量路径的所有值
   * @param {string} path 常量路径
   * @returns {Array} 值列表
   */
  getValues(path) {
    const obj = this.get(path);
    return obj && typeof obj === 'object' ? Object.values(obj) : [];
  }

  /**
   * 获取枚举选项列表
   * @param {string} enumPath 枚举路径
   * @returns {Array} 选项列表 [{value, label}]
   */
  getEnumOptions(enumPath) {
    const enumObj = this.get(enumPath);
    const namesObj = this.get(enumPath.replace(/\.([A-Z_]+)$/, '.${1}_NAMES'));
    
    if (!enumObj) return [];

    return Object.entries(enumObj).map(([key, value]) => ({
      value,
      label: namesObj ? namesObj[value] : value,
      key
    }));
  }

  /**
   * 验证数据
   * @param {*} value 要验证的值
   * @param {string} type 验证类型
   * @param {Object} options 验证选项
   * @returns {Object} 验证结果 {valid, message}
   */
  validate(value, type, options = {}) {
    const validation = this.getValidation(type);
    
    if (!validation) {
      return { valid: false, message: '未知的验证类型' };
    }

    switch (type) {
    case 'PHONE_REGEX':
      return {
        valid: validation.test(value),
        message: validation.test(value) ? '' : '手机号格式不正确'
      };
      
    case 'EMAIL_REGEX':
      return {
        valid: validation.test(value),
        message: validation.test(value) ? '' : '邮箱格式不正确'
      };
      
    case 'PASSWORD_REGEX':
      return {
        valid: validation.test(value),
        message: validation.test(value) ? '' : '密码必须包含字母和数字，长度6-20位'
      };

    case 'NUMBER_RANGES':
      const range = validation[options.range];
      if (!range) {
        return { valid: false, message: '未知的数值范围' };
      }
      const num = parseFloat(value);
      const valid = num >= range.min && num <= range.max;
      return {
        valid,
        message: valid ? '' : `值应在${range.min}-${range.max}之间`
      };

    case 'LENGTH_LIMITS':
      const limit = validation[options.field];
      if (!limit) {
        return { valid: false, message: '未知的长度限制' };
      }
      const len = value ? value.toString().length : 0;
      const lengthValid = len >= limit.min && len <= limit.max;
      return {
        valid: lengthValid,
        message: lengthValid ? '' : `长度应在${limit.min}-${limit.max}之间`
      };

    default:
      return { valid: true, message: '' };
    }
  }

  /**
   * 构建搜索索引
   * @returns {Map} 搜索索引
   */
  buildSearchIndex() {
    const index = new Map();
    
    const traverse = (obj, path = '') => {
      for (const [key, value] of Object.entries(obj)) {
        const currentPath = path ? `${path}.${key}` : key;
        
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          traverse(value, currentPath);
        } else {
          index.set(currentPath, value);
        }
      }
    };

    traverse(this.constants);
    return index;
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存信息
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * 导出常量为JSON
   * @param {string} path 常量路径，为空则导出所有
   * @returns {string} JSON字符串
   */
  exportToJSON(path = '') {
    const data = path ? this.get(path) : this.constants;
    return JSON.stringify(data, null, 2);
  }

  /**
   * 获取常量使用统计
   * @returns {Object} 使用统计
   */
  getUsageStats() {
    const stats = {};
    
    for (const [path] of this.cache.entries()) {
      const module = path.split('.')[0];
      stats[module] = (stats[module] || 0) + 1;
    }

    return stats;
  }
}

// 创建全局实例
const constantsManager = new ConstantsManager();

// 便捷方法
const Constants = {
  // 获取常量
  get: (path) => constantsManager.get(path),
  
  // 业务常量
  business: (module, key) => constantsManager.getBusiness(module, key),
  
  // 配置常量
  config: (module, key) => constantsManager.getConfig(module, key),
  
  // UI常量
  ui: (key) => constantsManager.getUI(key),
  
  // 图片常量
  image: (category, key) => constantsManager.getImage(category, key),
  
  // API常量
  api: (key) => constantsManager.getAPI(key),
  
  // 验证
  validate: (value, type, options) => constantsManager.validate(value, type, options),
  
  // 搜索
  search: (keyword) => constantsManager.search(keyword),
  
  // 枚举选项
  enumOptions: (enumPath) => constantsManager.getEnumOptions(enumPath),
  
  // 枚举名称
  enumName: (enumPath, value) => constantsManager.getEnumName(enumPath, value),
  
  // 管理器实例
  manager: constantsManager
};

module.exports = {
  ConstantsManager,
  Constants,
  constantsManager
};