/**
 * SAAS管理后台 - 最终全面测试脚本
 * 针对正确的SAAS平台管理后台进行测试
 */

const { chromium } = require('playwright');

class SaasAdminTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:4000';
    this.apiUrl = 'http://localhost:3000';
    this.testResults = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  async init() {
    console.log('🚀 启动SAAS管理后台最终测试...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 500
    });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    
    // 监听错误
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ 控制台错误:', msg.text());
      }
    });
  }

  async login() {
    console.log('\n📝 执行登录...');
    
    try {
      await this.page.goto(this.baseUrl);
      await this.page.waitForLoadState('networkidle');
      
      // 登录
      await this.page.fill('input[name="username"]', 'admin');
      await this.page.fill('input[name="password"]', 'admin123');
      await this.page.click('button[type="submit"]');
      
      await this.page.waitForURL('**/dashboard', { timeout: 10000 });
      console.log('✅ 登录成功');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 登录失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Login: ${error.message}`);
    }
  }

  async testDashboard() {
    console.log('\n📊 测试仪表板...');
    
    try {
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForLoadState('networkidle');
      
      // 检查页面标题
      const title = await this.page.textContent('h1, .page-title, .content-header h1');
      console.log('📊 页面标题:', title);
      
      // 检查统计卡片
      const cards = await this.page.locator('.card, .info-box, .small-box').count();
      console.log('📈 统计卡片数量:', cards);
      
      console.log('✅ 仪表板测试通过');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 仪表板测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Dashboard: ${error.message}`);
    }
  }

  async testSaasMenuItems() {
    console.log('\n🧭 测试SAAS管理后台菜单...');
    
    const menuItems = [
      { name: '租户管理', selector: 'a[href="/tenants"]', url: '/tenants' },
      { name: '平台用户', selector: 'a[href="/platform-users"]', url: '/platform-users' },
      { name: '今日鹅价', selector: 'a[href="/goose-prices"]', url: '/goose-prices' },
      { name: '知识库管理', selector: 'a[href="/knowledge"]', url: '/knowledge' },
      { name: '公告管理', selector: 'a[href="/announcements"]', url: '/announcements' },
      { name: '商城管理', selector: 'a[href="/mall/products"]', url: '/mall/products' },
      { name: '库存管理', selector: 'a[href="/mall/inventory"]', url: '/mall/inventory' }
    ];
    
    for (const item of menuItems) {
      try {
        console.log(`🔍 测试菜单项: ${item.name}`);
        
        // 回到仪表板
        await this.page.goto(`${this.baseUrl}/dashboard`);
        await this.page.waitForLoadState('networkidle');
        
        // 查找并点击菜单项
        const menuLink = this.page.locator(item.selector).first();
        
        if (await menuLink.count() > 0) {
          // 如果菜单项在下拉菜单中，先展开父菜单
          const parentItem = await menuLink.locator('..').locator('..').locator('..').locator('a').first();
          if (await parentItem.getAttribute('class') && (await parentItem.getAttribute('class')).includes('nav-link')) {
            const hasSubmenu = await parentItem.locator('i.fa-angle-left').count() > 0;
            if (hasSubmenu) {
              await parentItem.click();
              await this.page.waitForTimeout(500); // 等待下拉菜单展开
            }
          }
          
          await menuLink.click();
          await this.page.waitForLoadState('networkidle');
          
          const currentUrl = this.page.url();
          console.log(`   ✅ ${item.name} 页面加载成功: ${currentUrl}`);
          
          // 检查页面是否有内容
          const hasContent = await this.page.locator('h1, .content-header, .page-title').count() > 0;
          if (hasContent) {
            console.log(`   ✅ ${item.name} 页面有内容`);
          } else {
            console.log(`   ⚠️ ${item.name} 页面可能缺少内容`);
          }
          
        } else {
          console.log(`   ⚠️ ${item.name} 菜单项未找到`);
        }
        
      } catch (error) {
        console.log(`   ❌ ${item.name} 测试失败: ${error.message}`);
        this.testResults.errors.push(`Menu ${item.name}: ${error.message}`);
      }
    }
    
    console.log('✅ SAAS菜单测试完成');
    this.testResults.passed++;
  }

  async testUserDropdownMenu() {
    console.log('\n👤 测试用户下拉菜单...');
    
    try {
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForLoadState('networkidle');
      
      // 点击用户下拉菜单
      const userDropdown = this.page.locator('#navbarDropdown, .dropdown-toggle').first();
      await userDropdown.click();
      await this.page.waitForTimeout(500);
      
      // 测试下拉菜单项
      const dropdownItems = [
        { name: '个人资料', selector: 'a[href="/users/profile"]' },
        { name: '系统设置', selector: 'a[href="/system/settings"]' },
        { name: '系统日志', selector: 'a[href="/system/logs"]' }
      ];
      
      for (const item of dropdownItems) {
        const link = this.page.locator(item.selector);
        if (await link.count() > 0) {
          console.log(`   ✅ ${item.name} 菜单项存在`);
        } else {
          console.log(`   ⚠️ ${item.name} 菜单项未找到`);
        }
      }
      
      console.log('✅ 用户下拉菜单测试完成');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 用户下拉菜单测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`User Dropdown: ${error.message}`);
    }
  }

  async testApiEndpoints() {
    console.log('\n🔌 测试API端点状态...');
    
    const endpoints = [
      { name: '健康检查', url: '/api/health' },
      { name: '鹅群API', url: '/api/v1/flocks' },
      { name: '健康记录API', url: '/api/v1/health/records' },
      { name: '用户API', url: '/api/v1/users' },
      { name: '生产记录API', url: '/api/v1/production-records' }
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await this.page.request.get(`${this.apiUrl}${endpoint.url}`);
        const status = response.status();
        
        if (status === 200) {
          console.log(`   ✅ ${endpoint.name} API正常 (${status})`);
        } else if (status === 401) {
          console.log(`   ✅ ${endpoint.name} API正确要求认证 (${status})`);
        } else {
          console.log(`   ⚠️ ${endpoint.name} API状态异常 (${status})`);
        }
        
      } catch (error) {
        console.log(`   ❌ ${endpoint.name} API测试失败: ${error.message}`);
      }
    }
    
    this.testResults.passed++;
  }

  async testResponsiveness() {
    console.log('\n📱 测试响应式设计...');
    
    try {
      const viewports = [
        { name: '桌面', width: 1920, height: 1080 },
        { name: '平板', width: 768, height: 1024 },
        { name: '手机', width: 375, height: 667 }
      ];
      
      for (const viewport of viewports) {
        await this.page.setViewportSize({ width: viewport.width, height: viewport.height });
        await this.page.goto(`${this.baseUrl}/dashboard`);
        await this.page.waitForLoadState('networkidle');
        
        // 检查侧边栏是否正确响应
        const sidebar = this.page.locator('.main-sidebar').first();
        const isVisible = await sidebar.isVisible();
        
        console.log(`   📱 ${viewport.name} (${viewport.width}x${viewport.height}): 侧边栏${isVisible ? '可见' : '隐藏'}`);
      }
      
      // 恢复桌面视图
      await this.page.setViewportSize({ width: 1920, height: 1080 });
      
      console.log('✅ 响应式测试完成');
      this.testResults.passed++;
      
    } catch (error) {
      console.log('❌ 响应式测试失败:', error.message);
      this.testResults.failed++;
      this.testResults.errors.push(`Responsive: ${error.message}`);
    }
  }

  async generateFinalReport() {
    console.log('\n📊 生成最终测试报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      testType: 'SAAS管理后台全面测试',
      summary: {
        total: this.testResults.passed + this.testResults.failed,
        passed: this.testResults.passed,
        failed: this.testResults.failed,
        success_rate: `${((this.testResults.passed / (this.testResults.passed + this.testResults.failed)) * 100).toFixed(2)}%`
      },
      errors: this.testResults.errors,
      status: this.testResults.failed === 0 ? 'PASS' : 'PARTIAL_PASS'
    };
    
    console.log('\n🎯 最终测试报告:');
    console.log('=====================================');
    console.log(`📊 总测试数: ${report.summary.total}`);
    console.log(`✅ 通过: ${report.summary.passed}`);
    console.log(`❌ 失败: ${report.summary.failed}`);
    console.log(`📈 成功率: ${report.summary.success_rate}`);
    console.log(`🏆 总体状态: ${report.status}`);
    
    if (report.errors.length > 0) {
      console.log('\n❌ 发现的问题:');
      report.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    } else {
      console.log('\n🎉 所有测试都通过了！SAAS管理后台运行完美！');
    }
    
    return report;
  }

  async runAllTests() {
    try {
      await this.init();
      
      await this.login();
      await this.testDashboard();
      await this.testSaasMenuItems();
      await this.testUserDropdownMenu();
      await this.testApiEndpoints();
      await this.testResponsiveness();
      
      const report = await this.generateFinalReport();
      
      return report;
      
    } catch (error) {
      console.log('❌ 测试执行失败:', error.message);
      throw error;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 执行测试
async function main() {
  const tester = new SaasAdminTester();
  
  try {
    const report = await tester.runAllTests();
    
    console.log('\n🎉 SAAS管理后台测试完成！');
    
    // 保存报告
    const fs = require('fs');
    fs.writeFileSync('saas-admin-final-report.json', JSON.stringify(report, null, 2));
    console.log('📄 最终报告已保存到: saas-admin-final-report.json');
    
    // 根据测试结果设置退出码
    process.exit(report.status === 'PASS' ? 0 : 1);
    
  } catch (error) {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = SaasAdminTester;
