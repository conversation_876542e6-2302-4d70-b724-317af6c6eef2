#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');

// 测试模块列表
const modules = [
    { name: '仪表盘', path: '/dashboard' },
    { name: '用户管理', path: '/users' },
    { name: '租户管理', path: '/tenants' },
    { name: '鹅群管理', path: '/flocks' },
    { name: '生产管理', path: '/production' },
    { name: '健康管理', path: '/health' },
    { name: '财务管理', path: '/finance' },
    { name: '库存管理', path: '/inventory' },
    { name: '统计报告', path: '/reports' },
    { name: '系统管理', path: '/system' },
    { name: '鹅价管理', path: '/goose-prices' },
    { name: '商城管理', path: '/mall' },
    { name: '知识库', path: '/knowledge' },
    { name: '公告管理', path: '/announcements' },
    { name: 'API管理', path: '/api-management' },
    { name: '平台用户', path: '/platform-users' }
];

// API接口测试列表
const apiEndpoints = [
    { name: '仪表盘统计', path: '/api/dashboard/stats' },
    { name: '用户列表', path: '/api/users/list' },
    { name: '健康检查', path: '/api/health' }
];

async function testModules() {
    console.log('🚀 开始测试智慧养鹅SAAS后台管理系统所有模块...\n');
    
    const results = {
        success: [],
        failed: [],
        errors: []
    };

    // 首先进行登录获取session
    try {
        const loginResponse = await axios.post('http://localhost:4000/auth/login', {
            username: 'admin',
            password: 'admin123'
        }, {
            withCredentials: true,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ 登录成功');
        
        // 保存cookies用于后续请求
        const cookies = loginResponse.headers['set-cookie'];
        
        // 测试页面模块
        console.log('\n📄 测试页面模块访问性...');
        for (const module of modules) {
            try {
                const response = await axios.get(`http://localhost:4000${module.path}`, {
                    headers: {
                        'Cookie': cookies ? cookies.join('; ') : ''
                    },
                    timeout: 5000,
                    validateStatus: function (status) {
                        return status < 500; // 接受所有小于500的状态码
                    }
                });
                
                if (response.status === 200) {
                    console.log(`✅ ${module.name} (${module.path}): ${response.status}`);
                    results.success.push({
                        name: module.name,
                        path: module.path,
                        status: response.status,
                        type: 'page'
                    });
                } else {
                    console.log(`⚠️  ${module.name} (${module.path}): ${response.status}`);
                    results.failed.push({
                        name: module.name,
                        path: module.path,
                        status: response.status,
                        type: 'page'
                    });
                }
            } catch (error) {
                console.log(`❌ ${module.name} (${module.path}): ${error.message}`);
                results.errors.push({
                    name: module.name,
                    path: module.path,
                    error: error.message,
                    type: 'page'
                });
            }
        }
        
        // 测试API接口
        console.log('\n🔌 测试API接口...');
        for (const api of apiEndpoints) {
            try {
                const response = await axios.get(`http://localhost:4000${api.path}`, {
                    headers: {
                        'Cookie': cookies ? cookies.join('; ') : ''
                    },
                    timeout: 5000,
                    validateStatus: function (status) {
                        return status < 500;
                    }
                });
                
                if (response.status === 200) {
                    console.log(`✅ ${api.name} (${api.path}): ${response.status}`);
                    results.success.push({
                        name: api.name,
                        path: api.path,
                        status: response.status,
                        type: 'api'
                    });
                } else {
                    console.log(`⚠️  ${api.name} (${api.path}): ${response.status}`);
                    results.failed.push({
                        name: api.name,
                        path: api.path,
                        status: response.status,
                        type: 'api'
                    });
                }
            } catch (error) {
                console.log(`❌ ${api.name} (${api.path}): ${error.message}`);
                results.errors.push({
                    name: api.name,
                    path: api.path,
                    error: error.message,
                    type: 'api'
                });
            }
        }
        
    } catch (loginError) {
        console.log('❌ 登录失败:', loginError.message);
        return;
    }

    // 生成测试报告
    console.log('\n📊 测试结果汇总:');
    console.log(`✅ 成功: ${results.success.length}`);
    console.log(`⚠️  失败: ${results.failed.length}`);
    console.log(`❌ 错误: ${results.errors.length}`);
    
    // 保存详细报告
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            total: modules.length + apiEndpoints.length,
            success: results.success.length,
            failed: results.failed.length,
            errors: results.errors.length
        },
        results: results
    };
    
    fs.writeFileSync('module-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 详细报告已保存到: module-test-report.json');
    
    return results;
}

// 运行测试
testModules().catch(console.error);
