/**
 * 测试修复后的API端点
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3000';
const ADMIN_BASE = 'http://localhost:4000';

async function testApiEndpoints() {
  console.log('🔍 测试API端点修复效果...\n');

  const endpoints = [
    { name: '健康检查', url: `${API_BASE}/api/health`, expectAuth: false },
    { name: '鹅群列表', url: `${API_BASE}/api/v1/flocks`, expectAuth: true },
    { name: '健康记录', url: `${API_BASE}/api/v1/health/records`, expectAuth: true },
    { name: '用户列表', url: `${API_BASE}/api/v1/users`, expectAuth: true },
    { name: '生产记录', url: `${API_BASE}/api/v1/production-records`, expectAuth: true }
  ];

  const results = {
    passed: 0,
    failed: 0,
    details: []
  };

  for (const endpoint of endpoints) {
    try {
      console.log(`🔍 测试: ${endpoint.name}`);
      
      const response = await axios.get(endpoint.url, {
        timeout: 5000,
        validateStatus: () => true // 接受所有状态码
      });

      const status = response.status;
      const data = response.data;

      if (endpoint.expectAuth) {
        // 期望需要认证的端点
        if (status === 401 || (status === 200 && data.success === false && data.message?.includes('令牌'))) {
          console.log(`   ✅ ${endpoint.name} - 正确要求认证 (${status})`);
          results.passed++;
          results.details.push({ endpoint: endpoint.name, status: 'PASS', message: '正确要求认证' });
        } else if (status === 404) {
          console.log(`   ❌ ${endpoint.name} - 端点不存在 (${status})`);
          results.failed++;
          results.details.push({ endpoint: endpoint.name, status: 'FAIL', message: '端点不存在' });
        } else {
          console.log(`   ⚠️ ${endpoint.name} - 意外响应 (${status}): ${JSON.stringify(data).substring(0, 100)}`);
          results.passed++; // 至少端点存在
          results.details.push({ endpoint: endpoint.name, status: 'WARN', message: '意外响应但端点存在' });
        }
      } else {
        // 期望无需认证的端点
        if (status === 200 && data.success !== false) {
          console.log(`   ✅ ${endpoint.name} - 正常响应 (${status})`);
          results.passed++;
          results.details.push({ endpoint: endpoint.name, status: 'PASS', message: '正常响应' });
        } else {
          console.log(`   ❌ ${endpoint.name} - 异常响应 (${status}): ${JSON.stringify(data).substring(0, 100)}`);
          results.failed++;
          results.details.push({ endpoint: endpoint.name, status: 'FAIL', message: '异常响应' });
        }
      }

    } catch (error) {
      console.log(`   ❌ ${endpoint.name} - 请求失败: ${error.message}`);
      results.failed++;
      results.details.push({ endpoint: endpoint.name, status: 'FAIL', message: `请求失败: ${error.message}` });
    }
  }

  return results;
}

async function testAdminPages() {
  console.log('\n🌐 测试管理后台页面...\n');

  const pages = [
    { name: '登录页面', url: `${ADMIN_BASE}/auth/login` },
    { name: '首页重定向', url: `${ADMIN_BASE}/` },
    { name: '仪表板', url: `${ADMIN_BASE}/dashboard` }
  ];

  const results = {
    passed: 0,
    failed: 0,
    details: []
  };

  for (const page of pages) {
    try {
      console.log(`🔍 测试: ${page.name}`);
      
      const response = await axios.get(page.url, {
        timeout: 10000,
        validateStatus: () => true,
        maxRedirects: 0 // 不自动跟随重定向
      });

      const status = response.status;

      if (status === 200) {
        console.log(`   ✅ ${page.name} - 页面正常加载 (${status})`);
        results.passed++;
        results.details.push({ page: page.name, status: 'PASS', message: '页面正常加载' });
      } else if (status === 302) {
        const location = response.headers.location;
        console.log(`   ✅ ${page.name} - 正确重定向到: ${location} (${status})`);
        results.passed++;
        results.details.push({ page: page.name, status: 'PASS', message: `重定向到: ${location}` });
      } else {
        console.log(`   ❌ ${page.name} - 异常状态 (${status})`);
        results.failed++;
        results.details.push({ page: page.name, status: 'FAIL', message: `异常状态: ${status}` });
      }

    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.log(`   ❌ ${page.name} - 服务未启动`);
        results.failed++;
        results.details.push({ page: page.name, status: 'FAIL', message: '服务未启动' });
      } else {
        console.log(`   ❌ ${page.name} - 请求失败: ${error.message}`);
        results.failed++;
        results.details.push({ page: page.name, status: 'FAIL', message: `请求失败: ${error.message}` });
      }
    }
  }

  return results;
}

async function generateReport(apiResults, adminResults) {
  console.log('\n📊 测试报告');
  console.log('='.repeat(50));
  
  console.log('\n🔌 API端点测试结果:');
  console.log(`✅ 通过: ${apiResults.passed}`);
  console.log(`❌ 失败: ${apiResults.failed}`);
  console.log(`📈 成功率: ${((apiResults.passed / (apiResults.passed + apiResults.failed)) * 100).toFixed(1)}%`);
  
  console.log('\n🌐 管理后台测试结果:');
  console.log(`✅ 通过: ${adminResults.passed}`);
  console.log(`❌ 失败: ${adminResults.failed}`);
  console.log(`📈 成功率: ${((adminResults.passed / (adminResults.passed + adminResults.failed)) * 100).toFixed(1)}%`);
  
  const totalPassed = apiResults.passed + adminResults.passed;
  const totalFailed = apiResults.failed + adminResults.failed;
  const overallSuccess = ((totalPassed / (totalPassed + totalFailed)) * 100).toFixed(1);
  
  console.log('\n🎯 总体结果:');
  console.log(`✅ 总通过: ${totalPassed}`);
  console.log(`❌ 总失败: ${totalFailed}`);
  console.log(`📈 总成功率: ${overallSuccess}%`);
  
  // 详细结果
  console.log('\n📋 详细结果:');
  console.log('\nAPI端点:');
  apiResults.details.forEach(detail => {
    const icon = detail.status === 'PASS' ? '✅' : detail.status === 'WARN' ? '⚠️' : '❌';
    console.log(`  ${icon} ${detail.endpoint}: ${detail.message}`);
  });
  
  console.log('\n管理后台页面:');
  adminResults.details.forEach(detail => {
    const icon = detail.status === 'PASS' ? '✅' : '❌';
    console.log(`  ${icon} ${detail.page}: ${detail.message}`);
  });

  // 修复建议
  console.log('\n💡 修复建议:');
  const failedApis = apiResults.details.filter(d => d.status === 'FAIL');
  const failedPages = adminResults.details.filter(d => d.status === 'FAIL');
  
  if (failedApis.length > 0) {
    console.log('API端点问题:');
    failedApis.forEach(api => {
      console.log(`  - ${api.endpoint}: ${api.message}`);
    });
  }
  
  if (failedPages.length > 0) {
    console.log('管理后台问题:');
    failedPages.forEach(page => {
      console.log(`  - ${page.page}: ${page.message}`);
    });
  }
  
  if (failedApis.length === 0 && failedPages.length === 0) {
    console.log('🎉 所有测试都通过了！系统运行正常。');
  }

  return {
    api: apiResults,
    admin: adminResults,
    overall: {
      passed: totalPassed,
      failed: totalFailed,
      successRate: overallSuccess
    }
  };
}

async function main() {
  try {
    console.log('🚀 开始全面测试修复效果...\n');
    
    const apiResults = await testApiEndpoints();
    const adminResults = await testAdminPages();
    
    const report = await generateReport(apiResults, adminResults);
    
    // 保存报告
    const fs = require('fs');
    fs.writeFileSync('fix-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 详细报告已保存到: fix-test-report.json');
    
    // 返回退出码
    process.exit(report.overall.failed > 0 ? 1 : 0);
    
  } catch (error) {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { testApiEndpoints, testAdminPages, generateReport };
