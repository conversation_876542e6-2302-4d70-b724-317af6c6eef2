{"timestamp": "2025-08-26T14:30:27.422Z", "criticalModules": [{"name": "API管理", "path": "/api-management", "status": 200, "responseTime": 14, "contentLength": 32014, "hasError": true, "errorDetails": "页面包含错误信息", "headers": {"content-security-policy": "default-src 'self';script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com;style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;img-src 'self' data: https:;connect-src 'self';base-uri 'self';form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "content-type": "text/html; charset=utf-8", "etag": "W/\"8022-s6ipodR9QXRZ1DrinZVfuIujp9o\"", "set-cookie": "connect.sid=s%3A1woG1M5vUSF2pdoblTYattvfZtxObfbP.jqznJrbU%2FlVNNa2Bk%2FxdeEAR4821ZPZ9fng2DMgnfNE; Path=/; Expires=Wed, 27 Aug 2025 14:30:27 GMT; HttpOnly", "date": "Tu<PERSON>, 26 Aug 2025 14:30:27 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "transfer-encoding": "chunked"}, "contentPreview": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>\n    API管理\n  </title>\n\n  <!-- Favicon -->\n  <link rel=\"icon\" type=\"image/x-icon\" href=\"/favicon.ico\">\n\n  <!-- AdminLTE CSS -->\n  <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css\">\n\n  <!-- Bootstrap 5 CSS -->\n  <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\""}, {"name": "统计报告", "path": "/reports", "status": 200, "responseTime": 5, "contentLength": 22979, "hasError": false, "errorDetails": null, "headers": {"content-security-policy": "default-src 'self';script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com;style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;img-src 'self' data: https:;connect-src 'self';base-uri 'self';form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "content-type": "text/html; charset=utf-8", "etag": "W/\"5c1d-HI5K8WKhmFtL+RdwwNo9o547DwQ\"", "date": "Tu<PERSON>, 26 Aug 2025 14:30:27 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "transfer-encoding": "chunked"}, "contentPreview": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>\n    统计报告\n  </title>\n\n  <!-- Favicon -->\n  <link rel=\"icon\" type=\"image/x-icon\" href=\"/favicon.ico\">\n\n  <!-- AdminLTE CSS -->\n  <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css\">\n\n  <!-- Bootstrap 5 CSS -->\n  <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"s"}, {"name": "系统设置", "path": "/system", "status": 200, "responseTime": 3, "contentLength": 22978, "hasError": false, "errorDetails": null, "headers": {"content-security-policy": "default-src 'self';script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://code.jquery.com;style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com;img-src 'self' data: https:;connect-src 'self';base-uri 'self';form-action 'self';frame-ancestors 'self';object-src 'none';script-src-attr 'none';upgrade-insecure-requests", "cross-origin-opener-policy": "same-origin", "cross-origin-resource-policy": "same-origin", "origin-agent-cluster": "?1", "referrer-policy": "no-referrer", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-content-type-options": "nosniff", "x-dns-prefetch-control": "off", "x-download-options": "noopen", "x-frame-options": "SAMEORIGIN", "x-permitted-cross-domain-policies": "none", "x-xss-protection": "0", "vary": "Origin, Accept-Encoding", "access-control-allow-credentials": "true", "content-type": "text/html; charset=utf-8", "etag": "W/\"5c1c-qDc3UCbOt0zc3tZvs62V9iMN9Ow\"", "date": "Tu<PERSON>, 26 Aug 2025 14:30:27 GMT", "connection": "keep-alive", "keep-alive": "timeout=5", "transfer-encoding": "chunked"}, "contentPreview": "<!DOCTYPE html>\n<html lang=\"zh-CN\">\n\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>\n    系统设置\n  </title>\n\n  <!-- Favicon -->\n  <link rel=\"icon\" type=\"image/x-icon\" href=\"/favicon.ico\">\n\n  <!-- AdminLTE CSS -->\n  <link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css\">\n\n  <!-- Bootstrap 5 CSS -->\n  <link href=\"https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css\" rel=\"s"}], "allModulesStatus": [{"name": "仪表盘", "path": "/dashboard", "status": 200, "success": true}, {"name": "用户管理", "path": "/users", "status": 200, "success": true}, {"name": "租户管理", "path": "/tenants", "status": 200, "success": true}, {"name": "鹅群管理", "path": "/flocks", "status": 200, "success": true}, {"name": "生产管理", "path": "/production", "status": 200, "success": true}, {"name": "健康管理", "path": "/health", "status": 200, "success": true}, {"name": "财务管理", "path": "/finance", "status": 200, "success": true}, {"name": "库存管理", "path": "/inventory", "status": 200, "success": true}, {"name": "统计报告", "path": "/reports", "status": 200, "success": true}, {"name": "系统管理", "path": "/system", "status": 200, "success": true}, {"name": "鹅价管理", "path": "/goose-prices", "status": 200, "success": true}, {"name": "商城管理", "path": "/mall", "status": 200, "success": true}, {"name": "知识库", "path": "/knowledge", "status": 200, "success": true}, {"name": "公告管理", "path": "/announcements", "status": 200, "success": true}, {"name": "API管理", "path": "/api-management", "status": 200, "success": true}, {"name": "平台用户", "path": "/platform-users", "status": 200, "success": true}], "summary": {"criticalTotal": 3, "criticalSuccess": 2, "criticalFailed": 1, "allTotal": 16, "allSuccess": 16, "allFailed": 0}}