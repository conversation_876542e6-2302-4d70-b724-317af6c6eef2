#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');

// 重点测试的关键模块
const criticalModules = [
    { name: 'API管理', path: '/api-management', description: 'API接口管理和监控' },
    { name: '统计报告', path: '/reports', description: '数据统计和报表生成' },
    { name: '系统设置', path: '/system', description: '系统配置和参数管理' }
];

// 所有模块完整列表（用于回归测试）
const allModules = [
    { name: '仪表盘', path: '/dashboard' },
    { name: '用户管理', path: '/users' },
    { name: '租户管理', path: '/tenants' },
    { name: '鹅群管理', path: '/flocks' },
    { name: '生产管理', path: '/production' },
    { name: '健康管理', path: '/health' },
    { name: '财务管理', path: '/finance' },
    { name: '库存管理', path: '/inventory' },
    { name: '统计报告', path: '/reports' },
    { name: '系统管理', path: '/system' },
    { name: '鹅价管理', path: '/goose-prices' },
    { name: '商城管理', path: '/mall' },
    { name: '知识库', path: '/knowledge' },
    { name: '公告管理', path: '/announcements' },
    { name: 'API管理', path: '/api-management' },
    { name: '平台用户', path: '/platform-users' }
];

async function performLogin() {
    try {
        console.log('🔐 正在进行管理员登录...');
        const loginResponse = await axios.post('http://localhost:4000/auth/login', {
            username: 'admin',
            password: 'admin123'
        }, {
            withCredentials: true,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (loginResponse.data.success) {
            console.log('✅ 登录成功');
            return loginResponse.headers['set-cookie'];
        } else {
            throw new Error('登录失败: ' + loginResponse.data.message);
        }
    } catch (error) {
        console.error('❌ 登录失败:', error.message);
        throw error;
    }
}

async function testModuleDetailed(module, cookies) {
    console.log(`\n🔍 详细测试模块: ${module.name} (${module.path})`);
    
    const result = {
        name: module.name,
        path: module.path,
        status: null,
        responseTime: 0,
        contentLength: 0,
        hasError: false,
        errorDetails: null,
        headers: {},
        contentPreview: ''
    };
    
    const startTime = Date.now();
    
    try {
        const response = await axios.get(`http://localhost:4000${module.path}`, {
            headers: {
                'Cookie': cookies ? cookies.join('; ') : '',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            },
            timeout: 10000,
            maxRedirects: 5,
            validateStatus: function (status) {
                return status < 600; // 接受所有状态码进行分析
            }
        });
        
        result.status = response.status;
        result.responseTime = Date.now() - startTime;
        result.contentLength = response.data ? response.data.length : 0;
        result.headers = response.headers;
        
        // 获取内容预览
        if (typeof response.data === 'string') {
            result.contentPreview = response.data.substring(0, 500);
        }
        
        // 分析响应状态
        if (response.status === 200) {
            console.log(`  ✅ 状态: ${response.status} OK`);
            console.log(`  ⏱️  响应时间: ${result.responseTime}ms`);
            console.log(`  📄 内容长度: ${result.contentLength} bytes`);
            
            // 检查是否包含错误页面
            if (response.data.includes('Server Error') || response.data.includes('错误')) {
                result.hasError = true;
                result.errorDetails = '页面包含错误信息';
                console.log(`  ⚠️  警告: 页面包含错误信息`);
            }
        } else if (response.status === 302) {
            console.log(`  🔄 重定向: ${response.status} -> ${response.headers.location}`);
        } else if (response.status === 404) {
            console.log(`  ❌ 页面不存在: ${response.status}`);
            result.hasError = true;
            result.errorDetails = '页面不存在';
        } else if (response.status === 500) {
            console.log(`  💥 服务器错误: ${response.status}`);
            result.hasError = true;
            result.errorDetails = '服务器内部错误';
        } else {
            console.log(`  ⚠️  异常状态: ${response.status}`);
            result.hasError = true;
            result.errorDetails = `HTTP ${response.status}`;
        }
        
    } catch (error) {
        result.hasError = true;
        result.responseTime = Date.now() - startTime;
        
        if (error.code === 'ECONNREFUSED') {
            result.errorDetails = '连接被拒绝 - 服务器可能未启动';
            console.log(`  💀 连接失败: 服务器未响应`);
        } else if (error.code === 'ETIMEDOUT') {
            result.errorDetails = '请求超时';
            console.log(`  ⏰ 请求超时`);
        } else {
            result.errorDetails = error.message;
            console.log(`  ❌ 请求失败: ${error.message}`);
        }
    }
    
    return result;
}

async function runCriticalModuleTest() {
    console.log('🚀 开始关键模块深度诊断测试...\n');
    
    const testResults = {
        timestamp: new Date().toISOString(),
        criticalModules: [],
        allModulesStatus: [],
        summary: {
            criticalTotal: criticalModules.length,
            criticalSuccess: 0,
            criticalFailed: 0,
            allTotal: allModules.length,
            allSuccess: 0,
            allFailed: 0
        }
    };
    
    let cookies = null;
    
    try {
        // 执行登录
        cookies = await performLogin();
        
        // 详细测试关键模块
        console.log('\n📋 关键模块详细测试:');
        console.log('=' .repeat(60));
        
        for (const module of criticalModules) {
            const result = await testModuleDetailed(module, cookies);
            testResults.criticalModules.push(result);
            
            if (!result.hasError && result.status === 200) {
                testResults.summary.criticalSuccess++;
            } else {
                testResults.summary.criticalFailed++;
            }
        }
        
        // 快速回归测试所有模块
        console.log('\n\n🔄 全模块回归测试:');
        console.log('=' .repeat(60));
        
        for (const module of allModules) {
            try {
                const response = await axios.get(`http://localhost:4000${module.path}`, {
                    headers: {
                        'Cookie': cookies ? cookies.join('; ') : ''
                    },
                    timeout: 5000,
                    validateStatus: function (status) {
                        return status < 600;
                    }
                });
                
                const moduleResult = {
                    name: module.name,
                    path: module.path,
                    status: response.status,
                    success: response.status === 200 && !response.data.includes('Server Error')
                };
                
                testResults.allModulesStatus.push(moduleResult);
                
                if (moduleResult.success) {
                    console.log(`  ✅ ${module.name}: ${response.status}`);
                    testResults.summary.allSuccess++;
                } else {
                    console.log(`  ❌ ${module.name}: ${response.status}`);
                    testResults.summary.allFailed++;
                }
                
            } catch (error) {
                console.log(`  💀 ${module.name}: ${error.message}`);
                testResults.allModulesStatus.push({
                    name: module.name,
                    path: module.path,
                    status: 'ERROR',
                    success: false,
                    error: error.message
                });
                testResults.summary.allFailed++;
            }
        }
        
    } catch (loginError) {
        console.error('❌ 无法进行测试，登录失败:', loginError.message);
        return null;
    }
    
    // 生成测试报告
    console.log('\n\n📊 测试结果汇总:');
    console.log('=' .repeat(60));
    console.log(`关键模块: ✅ ${testResults.summary.criticalSuccess}/${testResults.summary.criticalTotal} 成功`);
    console.log(`全部模块: ✅ ${testResults.summary.allSuccess}/${testResults.summary.allTotal} 成功`);
    console.log(`整体成功率: ${((testResults.summary.allSuccess / testResults.summary.allTotal) * 100).toFixed(1)}%`);
    
    // 保存详细报告
    fs.writeFileSync('critical-modules-test-report.json', JSON.stringify(testResults, null, 2));
    console.log('\n📄 详细报告已保存到: critical-modules-test-report.json');
    
    return testResults;
}

// 运行测试
runCriticalModuleTest().catch(console.error);
