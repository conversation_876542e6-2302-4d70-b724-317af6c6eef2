#!/usr/bin/env node

const axios = require('axios');
const fs = require('fs');

// 所有模块完整列表
const allModules = [
    { name: '仪表盘', path: '/dashboard', critical: true },
    { name: '用户管理', path: '/users', critical: true },
    { name: '租户管理', path: '/tenants', critical: true },
    { name: '鹅群管理', path: '/flocks', critical: false },
    { name: '生产管理', path: '/production', critical: false },
    { name: '健康管理', path: '/health', critical: false },
    { name: '财务管理', path: '/finance', critical: false },
    { name: '库存管理', path: '/inventory', critical: false },
    { name: '统计报告', path: '/reports', critical: true },
    { name: '系统设置', path: '/system', critical: true },
    { name: '鹅价管理', path: '/goose-prices', critical: false },
    { name: '商城管理', path: '/mall', critical: false },
    { name: '知识库', path: '/knowledge', critical: false },
    { name: '公告管理', path: '/announcements', critical: false },
    { name: 'API管理', path: '/api-management', critical: true },
    { name: '平台用户', path: '/platform-users', critical: true }
];

// API接口测试
const apiEndpoints = [
    { name: '仪表盘统计API', path: '/api/dashboard/stats' },
    { name: '用户列表API', path: '/api/users/list' },
    { name: '健康检查API', path: '/api/health' }
];

async function performLogin() {
    try {
        console.log('🔐 正在进行管理员登录...');
        const loginResponse = await axios.post('http://localhost:4000/auth/login', {
            username: 'admin',
            password: 'admin123'
        }, {
            withCredentials: true,
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (loginResponse.data.success) {
            console.log('✅ 登录成功');
            return loginResponse.headers['set-cookie'];
        } else {
            throw new Error('登录失败: ' + loginResponse.data.message);
        }
    } catch (error) {
        console.error('❌ 登录失败:', error.message);
        throw error;
    }
}

function isRealError(content) {
    // 更精确的错误检测，排除正常的业务词汇
    const errorPatterns = [
        /Server Error/i,
        /Internal Server Error/i,
        /500 错误/i,
        /404 错误/i,
        /页面不存在/i,
        /服务器错误/i,
        /Something went wrong/i,
        /ReferenceError/i,
        /TypeError/i,
        /SyntaxError/i
    ];
    
    return errorPatterns.some(pattern => pattern.test(content));
}

async function testModule(module, cookies) {
    const startTime = Date.now();
    
    try {
        const response = await axios.get(`http://localhost:4000${module.path}`, {
            headers: {
                'Cookie': cookies ? cookies.join('; ') : '',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
            },
            timeout: 10000,
            maxRedirects: 5,
            validateStatus: function (status) {
                return status < 600;
            }
        });
        
        const responseTime = Date.now() - startTime;
        const hasRealError = isRealError(response.data);
        
        return {
            name: module.name,
            path: module.path,
            critical: module.critical,
            status: response.status,
            responseTime: responseTime,
            contentLength: response.data ? response.data.length : 0,
            success: response.status === 200 && !hasRealError,
            hasRealError: hasRealError,
            errorDetails: hasRealError ? '页面包含真实错误信息' : null
        };
        
    } catch (error) {
        return {
            name: module.name,
            path: module.path,
            critical: module.critical,
            status: 'ERROR',
            responseTime: Date.now() - startTime,
            contentLength: 0,
            success: false,
            hasRealError: true,
            errorDetails: error.message
        };
    }
}

async function runFinalTest() {
    console.log('🚀 开始智慧养鹅SAAS系统最终综合测试...\n');
    
    const testResults = {
        timestamp: new Date().toISOString(),
        testDuration: 0,
        modules: [],
        apis: [],
        summary: {
            totalModules: allModules.length,
            successfulModules: 0,
            failedModules: 0,
            criticalModules: allModules.filter(m => m.critical).length,
            criticalSuccess: 0,
            criticalFailed: 0,
            totalApis: apiEndpoints.length,
            successfulApis: 0,
            failedApis: 0,
            overallSuccessRate: 0
        }
    };
    
    const testStartTime = Date.now();
    let cookies = null;
    
    try {
        // 执行登录
        cookies = await performLogin();
        
        // 测试所有页面模块
        console.log('\n📄 测试所有页面模块:');
        console.log('=' .repeat(80));
        
        for (const module of allModules) {
            const result = await testModule(module, cookies);
            testResults.modules.push(result);
            
            const statusIcon = result.success ? '✅' : '❌';
            const criticalMark = result.critical ? '🔥' : '  ';
            
            console.log(`${statusIcon} ${criticalMark} ${result.name.padEnd(12)} (${result.path.padEnd(20)}) - ${result.status} (${result.responseTime}ms)`);
            
            if (result.success) {
                testResults.summary.successfulModules++;
                if (result.critical) testResults.summary.criticalSuccess++;
            } else {
                testResults.summary.failedModules++;
                if (result.critical) testResults.summary.criticalFailed++;
                if (result.errorDetails) {
                    console.log(`    ⚠️  错误详情: ${result.errorDetails}`);
                }
            }
        }
        
        // 测试API接口
        console.log('\n🔌 测试API接口:');
        console.log('=' .repeat(80));
        
        for (const api of apiEndpoints) {
            const result = await testModule(api, cookies);
            testResults.apis.push(result);
            
            const statusIcon = result.success ? '✅' : '❌';
            console.log(`${statusIcon}    ${result.name.padEnd(20)} (${result.path.padEnd(25)}) - ${result.status} (${result.responseTime}ms)`);
            
            if (result.success) {
                testResults.summary.successfulApis++;
            } else {
                testResults.summary.failedApis++;
                if (result.errorDetails) {
                    console.log(`    ⚠️  错误详情: ${result.errorDetails}`);
                }
            }
        }
        
    } catch (loginError) {
        console.error('❌ 无法进行测试，登录失败:', loginError.message);
        return null;
    }
    
    // 计算测试持续时间和成功率
    testResults.testDuration = Date.now() - testStartTime;
    const totalItems = testResults.summary.totalModules + testResults.summary.totalApis;
    const totalSuccess = testResults.summary.successfulModules + testResults.summary.successfulApis;
    testResults.summary.overallSuccessRate = ((totalSuccess / totalItems) * 100).toFixed(1);
    
    // 生成测试报告
    console.log('\n\n📊 最终测试结果汇总:');
    console.log('=' .repeat(80));
    console.log(`🕒 测试耗时: ${testResults.testDuration}ms`);
    console.log(`📄 页面模块: ✅ ${testResults.summary.successfulModules}/${testResults.summary.totalModules} 成功`);
    console.log(`🔥 关键模块: ✅ ${testResults.summary.criticalSuccess}/${testResults.summary.criticalModules} 成功`);
    console.log(`🔌 API接口: ✅ ${testResults.summary.successfulApis}/${testResults.summary.totalApis} 成功`);
    console.log(`🎯 整体成功率: ${testResults.summary.overallSuccessRate}%`);
    
    // 系统健康评估
    let healthStatus = '🟢 优秀';
    if (testResults.summary.overallSuccessRate < 100) {
        healthStatus = testResults.summary.overallSuccessRate >= 90 ? '🟡 良好' : '🔴 需要关注';
    }
    console.log(`💊 系统健康状态: ${healthStatus}`);
    
    // 保存详细报告
    fs.writeFileSync('final-comprehensive-test-report.json', JSON.stringify(testResults, null, 2));
    console.log('\n📄 详细报告已保存到: final-comprehensive-test-report.json');
    
    return testResults;
}

// 运行最终测试
runFinalTest().catch(console.error);
